/* Portal Application Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #1e293b;
    background-color: #f8fafc;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Typography System */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
    color: #0f172a;
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

.text-slate-900 { color: #0f172a; }
.text-slate-700 { color: #334155; }
.text-slate-600 { color: #475569; }
.text-slate-500 { color: #64748b; }
.text-slate-400 { color: #94a3b8; }
.text-slate-300 { color: #cbd5e1; }

/* Login Page Styles */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.logo h1 {
    text-align: center;
    color: #667eea;
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s;
}

.btn-primary {
    background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e4a4b 0%, #2B5E5F 100%);
    box-shadow: 0 4px 12px rgba(43, 94, 95, 0.2);
}

.btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.3);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.btn-full {
    width: 100%;
}

.btn-sm {
    padding: 0.6rem 1.2rem;
    font-size: 0.875rem;
    border-radius: 12px;
    font-weight: 600;
}

/* Alert Styles */
.alert {
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-radius: 15px;
    border: 1px solid transparent;
    backdrop-filter: blur(10px);
    font-weight: 500;
}

.alert-error {
    background: rgba(248, 215, 218, 0.9);
    border-color: rgba(245, 198, 203, 0.8);
    color: #721c24;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.1);
}

.alert-success {
    background: rgba(212, 237, 218, 0.9);
    border-color: rgba(195, 230, 203, 0.8);
    color: #155724;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
}

.alert-info {
    background: rgba(209, 236, 241, 0.9);
    border-color: rgba(190, 229, 235, 0.8);
    color: #0c5460;
    box-shadow: 0 4px 15px rgba(43, 94, 95, 0.1);
}

/* Dashboard Styles */
.dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.dashboard::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover no-repeat;
    opacity: 0.02;
    z-index: -2;
    pointer-events: none;
}

.dashboard::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 85%, rgba(43, 94, 95, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 85% 15%, rgba(30, 74, 75, 0.08) 0%, transparent 50%),
        linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(226, 232, 240, 0.95) 100%);
    z-index: -1;
    pointer-events: none;
}

.header {
    background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 50%, #2B5E5F 100%);
    color: white;
    padding: 0;
    box-shadow: 0 8px 32px rgba(43, 94, 95, 0.3), 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
    overflow: visible;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    z-index: 1000;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        rgba(255,255,255,0.6) 0%,
        rgba(255,255,255,0.2) 25%,
        rgba(255,255,255,0.1) 50%,
        rgba(255,255,255,0.2) 75%,
        rgba(255,255,255,0.6) 100%);
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1.25rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
    min-height: 88px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.brand-section {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    position: relative;
}

.brand-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.brand-logo::after {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 12px;
    z-index: -1;
}

.logo-image {
    height: 52px;
    width: auto;
    max-width: 220px;
    object-fit: contain;
    filter: drop-shadow(0 3px 6px rgba(0,0,0,0.3)) brightness(1.1);
}

.brand-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

.brand-subtitle {
    font-size: 0.8rem;
    color: rgba(255,255,255,0.85);
    text-transform: uppercase;
    letter-spacing: 1.2px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}



.main-navigation {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(43, 94, 95, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid rgba(43, 94, 95, 0.2);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 6px;
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    white-space: nowrap;
    background: none;
    border: none;
    cursor: pointer;
}

.nav-item.active {
    background: rgba(43, 94, 95, 0.3);
    color: white;
    font-weight: 600;
}

.nav-item:hover {
    background: rgba(43, 94, 95, 0.2);
    color: white;
}

/* Modules Dropdown */
.modules-dropdown-container {
    position: relative;
}

.dropdown-toggle {
    background: none !important;
}

.dropdown-arrow {
    font-size: 0.7rem;
    margin-left: 0.5rem;
    color: rgba(255,255,255,0.7);
}

.modules-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 320px;
    z-index: 1000;
    display: none;
    margin-top: 0.5rem;
}

.modules-dropdown.show {
    display: block;
}

.modules-dropdown-header {
    padding: 1rem 1.25rem 0.5rem;
    border-bottom: 1px solid #f3f4f6;
}

.modules-dropdown-header h4 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
}

.modules-dropdown-list {
    padding: 0.5rem 0;
}

.module-dropdown-item {
    display: block;
    padding: 0.75rem 1.25rem;
    text-decoration: none;
    color: #374151;
    border-bottom: 1px solid #f9fafb;
}

.module-dropdown-item:last-child {
    border-bottom: none;
}

.module-dropdown-item:hover {
    background: #f9fafb;
}

.module-dropdown-title {
    font-weight: 600;
    font-size: 0.9rem;
    color: #2B5E5F;
    margin-bottom: 0.25rem;
}

.module-dropdown-desc {
    font-size: 0.8rem;
    color: #6b7280;
    line-height: 1.4;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 2rem;
}

/* Professional Actions Container */
.professional-actions {
    display: flex;
    align-items: center;
    gap: 2rem;
    position: relative;
    z-index: 1001;
}

/* Professional Link Styling */
.professional-link {
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    padding: 0.5rem 0;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.professional-link:hover {
    color: white;
    border-bottom-color: rgba(255,255,255,0.3);
}

/* Professional Button Styling */
.professional-btn {
    background: none;
    border: none;
    color: rgba(255,255,255,0.9);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.professional-btn:hover {
    background: rgba(255,255,255,0.1);
    color: white;
}

.action-btn {
    background: rgba(43, 94, 95, 0.1);
    border: 1px solid rgba(43, 94, 95, 0.2);
    color: rgba(255,255,255,0.9);
    padding: 0.75rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    font-size: 0.85rem;
}

.action-btn:hover {
    background: rgba(43, 94, 95, 0.2);
    color: white;
}

.action-btn.corporate-btn {
    background: rgba(43, 94, 95, 0.15);
    border-color: rgba(43, 94, 95, 0.3);
}

.action-btn.corporate-btn:hover {
    background: rgba(43, 94, 95, 0.25);
}

.btn-text {
    font-size: 0.85rem;
    font-weight: 500;
    white-space: nowrap;
}

/* User Menu Container */
.user-menu-container {
    position: relative;
    z-index: 1002;
}

.user-menu-trigger {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255,255,255,0.08);
    border: 1px solid rgba(255,255,255,0.15);
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    color: rgba(255,255,255,0.9);
    transition: all 0.2s ease;
}

.user-menu-trigger:hover {
    background: rgba(255,255,255,0.12);
    border-color: rgba(255,255,255,0.25);
    color: white;
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
    color: white;
    flex-shrink: 0;
}

.user-info-compact {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.user-name-compact {
    font-weight: 600;
    font-size: 0.9rem;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.user-role-compact {
    font-size: 0.75rem;
    color: rgba(255,255,255,0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.dropdown-chevron {
    color: rgba(255,255,255,0.6);
    flex-shrink: 0;
    transition: transform 0.2s ease;
}

.user-menu-trigger:hover .dropdown-chevron {
    color: rgba(255,255,255,0.8);
}

/* Dropdown Containers */
.notification-container,
.modules-dropdown-container {
    position: relative;
    z-index: 1002;
}

/* Dropdown Styling */
.notifications-dropdown {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(226, 232, 240, 0.8);
    min-width: 320px;
    max-width: 400px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 10000;
}

.notifications-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* User Menu Dropdown */
.user-menu-dropdown {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    min-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 10000;
}

.user-menu-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* User Menu Content */
.user-menu-header {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid #f3f4f6;
    background: #f9fafb;
}

.user-profile-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-name-full {
    font-weight: 600;
    font-size: 0.95rem;
    color: #111827;
}

.user-details-full {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.8rem;
}

.client-name {
    color: #6b7280;
}

.role-badge-small {
    background: #2B5E5F;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.user-menu-list {
    padding: 0.5rem 0;
}

.user-menu-item {
    display: block;
    padding: 0.75rem 1.25rem;
    color: #374151;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.15s ease;
}

.user-menu-item:hover {
    background: #f9fafb;
    color: #111827;
}

.user-menu-item.admin-item {
    color: #2B5E5F;
    font-weight: 600;
}

.user-menu-item.admin-item:hover {
    background: rgba(43, 94, 95, 0.05);
}

.user-menu-item.logout-item {
    color: #dc2626;
    font-weight: 600;
}

.user-menu-item.logout-item:hover {
    background: #fef2f2;
    color: #b91c1c;
}

.menu-divider {
    height: 1px;
    background: #f3f4f6;
    margin: 0.5rem 0;
}

/* Dropdown Arrow */
.notifications-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid rgba(255, 255, 255, 0.98);
    filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.1));
}

/* Dropdown Headers */
.notifications-header,
.settings-header {
    padding: 1.25rem 1.5rem 1rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notifications-header h4,
.settings-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
}

.mark-all-read {
    background: none;
    border: none;
    color: #2B5E5F;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.mark-all-read:hover {
    background: rgba(43, 94, 95, 0.1);
}

/* Notifications List */
.notifications-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem 0;
}

.notification-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.3);
    display: flex;
    gap: 1rem;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.notification-item:hover {
    background: rgba(248, 250, 252, 0.8);
}

.notification-item.unread {
    background: rgba(59, 130, 246, 0.02);
    border-left: 3px solid #3b82f6;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 8px;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.notification-text {
    color: #6b7280;
    font-size: 0.85rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.notification-time {
    color: #9ca3af;
    font-size: 0.75rem;
}

/* Notifications Footer */
.notifications-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    text-align: center;
}

.view-all-link {
    color: #2B5E5F;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.view-all-link:hover {
    color: #1e4a4b;
}

/* Settings List */
.settings-list {
    padding: 0.5rem 0;
}

.settings-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: #374151;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.settings-item:hover {
    background: rgba(248, 250, 252, 0.8);
    color: #1f2937;
}

.settings-item.logout {
    color: #ef4444;
}

.settings-item.logout:hover {
    background: rgba(239, 68, 68, 0.05);
    color: #dc2626;
}

.settings-divider {
    height: 1px;
    background: rgba(226, 232, 240, 0.5);
    margin: 0.5rem 0;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(226, 232, 240, 0.8);
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    z-index: 10000;
    max-width: 300px;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.toast-success {
    border-left: 4px solid #10b981;
    color: #065f46;
}

.toast.toast-error {
    border-left: 4px solid #ef4444;
    color: #991b1b;
}

.toast.toast-info {
    border-left: 4px solid #3b82f6;
    color: #1e40af;
}

/* Ensure main content doesn't interfere with dropdowns */
.main-content {
    position: relative;
    z-index: 1;
}

.dashboard-grid {
    position: relative;
    z-index: 1;
}

/* Interactive Widgets Styling */
.dashboard-widgets {
    margin: 4rem 0;
}

.widgets-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2.5rem;
    padding: 0 0.5rem;
}

.widgets-header h3 {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 800;
    margin: 0;
    letter-spacing: -0.5px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.widgets-header h3::before {
    content: '📊';
    margin-right: 1rem;
    font-size: 1.8rem;
    opacity: 0.8;
}

.widgets-controls {
    display: flex;
    gap: 0.75rem;
}

.widget-toggle {
    background: rgba(43, 94, 95, 0.08);
    border: 1px solid rgba(43, 94, 95, 0.2);
    border-radius: 12px;
    padding: 0.75rem;
    color: #2B5E5F;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.widget-toggle:hover {
    background: rgba(43, 94, 95, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(43, 94, 95, 0.15);
}

.widgets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
    gap: 2rem;
}

/* Widget Card Styling */
.widget-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 18px;
    box-shadow: 0 12px 40px rgba(43, 94, 95, 0.1), 0 4px 16px rgba(0,0,0,0.06);
    border: 1px solid rgba(255,255,255,0.9);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
}

.widget-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #2B5E5F, #1e4a4b, #2B5E5F);
}

.widget-card:hover {
    box-shadow: 0 20px 60px rgba(43, 94, 95, 0.15), 0 8px 24px rgba(0,0,0,0.08);
    transform: translateY(-6px);
}

.widget-card.collapsed {
    opacity: 0.7;
    transform: scale(0.98);
}

.widget-header {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.3);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.9));
    position: relative;
}

.widget-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(43, 94, 95, 0.1), transparent);
}

.widget-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    border-radius: 14px;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(43, 94, 95, 0.3);
    transition: all 0.3s ease;
}

.widget-card:hover .widget-icon {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(43, 94, 95, 0.4);
}

.widget-title {
    flex: 1;
    font-size: 1.2rem;
    font-weight: 700;
    color: #1f2937;
    letter-spacing: -0.3px;
}

.widget-expand {
    background: rgba(107, 114, 128, 0.1);
    border: 1px solid rgba(107, 114, 128, 0.2);
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.widget-expand:hover {
    background: rgba(107, 114, 128, 0.2);
    color: #374151;
    transform: scale(1.1);
}

.widget-content {
    padding: 2rem;
}

/* Payment Widget */
.payment-summary {
    text-align: center;
    position: relative;
}

.payment-summary::before {
    content: '💰';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 2rem;
    opacity: 0.1;
}

.payment-amount {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(43, 94, 95, 0.05), rgba(30, 74, 75, 0.05));
    border-radius: 12px;
    border: 1px solid rgba(43, 94, 95, 0.1);
}

.amount-label {
    display: block;
    color: #6b7280;
    font-size: 0.95rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.amount-value {
    display: block;
    color: #2B5E5F;
    font-size: 2.2rem;
    font-weight: 800;
    font-family: 'Courier New', monospace;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.payment-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Document Widget */
.document-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.document-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.document-item:hover {
    background: rgba(43, 94, 95, 0.05);
    transform: translateX(4px);
}

.doc-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.doc-info {
    flex: 1;
}

.doc-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.doc-count {
    color: #6b7280;
    font-size: 0.85rem;
}

.doc-action {
    color: #2B5E5F;
    font-weight: 600;
    font-size: 1.2rem;
}

/* Status Widget */
.status-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-indicator.online {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-indicator.warning {
    background: #f59e0b;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

.status-indicator.offline {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.status-info {
    flex: 1;
}

.status-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.status-desc {
    color: #6b7280;
    font-size: 0.85rem;
}

.status-time {
    color: #9ca3af;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-actions {
    text-align: center;
}

/* Activity Widget */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.activity-item:hover {
    background: rgba(248, 250, 252, 0.8);
}

.activity-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.activity-info {
    flex: 1;
}

.activity-desc {
    color: #1f2937;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #9ca3af;
    font-size: 0.75rem;
}

.activity-actions {
    text-align: center;
}

/* Interactive Invoice Dashboard */
.invoice-dashboard {
    margin: 2rem 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(43, 94, 95, 0.08), 0 5px 20px rgba(0,0,0,0.04);
    border: 1px solid rgba(255,255,255,0.9);
    overflow: hidden;
}

.dashboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.8));
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.dashboard-header h3 {
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.dashboard-actions {
    display: flex;
    gap: 0.75rem;
}

.refresh-btn {
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.refresh-btn:hover {
    background: linear-gradient(135deg, #1e4a4b, #163a3b);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(43, 94, 95, 0.3);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
}

/* Dashboard Cards */
.dashboard-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(43, 94, 95, 0.08), 0 2px 8px rgba(0,0,0,0.04);
    border: 1px solid rgba(255,255,255,0.9);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.dashboard-card:hover {
    box-shadow: 0 12px 40px rgba(43, 94, 95, 0.12), 0 4px 12px rgba(0,0,0,0.06);
}

.dashboard-card.expanded {
    box-shadow: 0 15px 50px rgba(43, 94, 95, 0.15), 0 5px 15px rgba(0,0,0,0.08);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.8));
}

.card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    border-radius: 12px;
    color: white;
    flex-shrink: 0;
}

.card-title {
    flex: 1;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
}

.card-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
    color: #6b7280;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-indicator.success {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-indicator.warning {
    background: #f59e0b;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

.status-indicator.demo {
    background: #6366f1;
    box-shadow: 0 0 8px rgba(99, 102, 241, 0.4);
}

.card-content {
    padding: 1.5rem;
}

.card-actions {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    background: rgba(248, 250, 252, 0.5);
    text-align: center;
}

/* Connection Card Specific */
.folder-path {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.path-label {
    color: #6b7280;
    font-weight: 500;
    font-size: 0.9rem;
}

.path-value {
    color: #1f2937;
    font-family: 'Courier New', monospace;
    background: rgba(248, 250, 252, 0.8);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.85rem;
}

.connection-details {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.detail-label {
    color: #6b7280;
    font-size: 0.85rem;
}

.detail-value {
    color: #1f2937;
    font-weight: 500;
    font-size: 0.85rem;
}

.detail-value.warning {
    color: #f59e0b;
}

/* Payment Card Specific */
.payment-stats {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    flex: 1;
    text-align: center;
    padding: 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.stat-item.paid {
    background: rgba(16, 185, 129, 0.05);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.stat-item.paid:hover {
    background: rgba(16, 185, 129, 0.1);
    transform: translateY(-2px);
}

.stat-item.unpaid {
    background: rgba(245, 158, 11, 0.05);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.stat-item.unpaid:hover {
    background: rgba(245, 158, 11, 0.1);
    transform: translateY(-2px);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #6b7280;
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.stat-amount {
    color: #2B5E5F;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.stat-divider {
    width: 1px;
    height: 60px;
    background: rgba(226, 232, 240, 0.5);
    flex-shrink: 0;
}

.payment-progress {
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(226, 232, 240, 0.5);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-label {
    color: #6b7280;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Actions Card Specific */
.action-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.action-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-item:hover {
    background: rgba(43, 94, 95, 0.05);
    transform: translateX(4px);
}

.action-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.action-info {
    flex: 1;
}

.action-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.action-desc {
    color: #6b7280;
    font-size: 0.85rem;
}

.action-arrow {
    color: #2B5E5F;
    font-weight: 600;
    font-size: 1.2rem;
}

.notification-btn {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1;
}

/* Old user profile styles removed - now using professional user menu */

.user-info .separator {
    color: rgba(255,255,255,0.5);
    font-weight: 300;
}

.user-info .role {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

/* Enhanced Layout System */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 3rem 2rem 4rem;
    position: relative;
    z-index: 1;
    min-height: calc(100vh - 80px);
}

/* Spacing Utilities */
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

.space-x-1 > * + * { margin-left: 0.25rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

/* Container System */
.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

.container-sm { max-width: 640px; }
.container-md { max-width: 768px; }
.container-lg { max-width: 1024px; }
.container-xl { max-width: 1280px; }
.container-2xl { max-width: 1536px; }

/* Grid System */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-12 { gap: 3rem; }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

/* Welcome Section */
.welcome-section {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-radius: 24px;
    padding: 3rem;
    margin-bottom: 3rem;
    box-shadow: 0 15px 50px rgba(43, 94, 95, 0.12), 0 4px 16px rgba(0,0,0,0.08);
    border: 1px solid rgba(255,255,255,0.9);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease-out;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #2B5E5F 0%, #1e4a4b 50%, #2B5E5F 100%);
}

.welcome-section::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(43, 94, 95, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.welcome-section h2 {
    color: #2B5E5F;
    font-size: 2.4rem;
    font-weight: 800;
    margin-bottom: 1.2rem;
    letter-spacing: -0.8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.05);
    position: relative;
    z-index: 1;
}



.welcome-section p {
    color: #64748b;
    font-size: 1.15rem;
    line-height: 1.7;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

/* Module Grid */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2.5rem;
    margin-top: 3rem;
}

.module-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 12px 40px rgba(43, 94, 95, 0.1), 0 4px 16px rgba(0,0,0,0.06);
    text-decoration: none;
    color: inherit;
    border: 1px solid rgba(255,255,255,0.9);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 320px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--module-color, #2B5E5F), rgba(43, 94, 95, 0.5), var(--module-color, #2B5E5F));
}

.module-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(43, 94, 95, 0.03) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(40%, -40%);
    transition: all 0.4s ease;
}

.module-card:hover {
    box-shadow: 0 20px 60px rgba(43, 94, 95, 0.15), 0 8px 24px rgba(0,0,0,0.08);
    transform: translateY(-8px);
}

.module-card:hover::after {
    transform: translate(30%, -30%) scale(1.2);
    opacity: 0.8;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2.5rem 2.5rem 1.5rem;
    position: relative;
    z-index: 2;
}

.module-icon {
    width: 72px;
    height: 72px;
    border-radius: 18px;
    background: linear-gradient(135deg, var(--module-color, #2B5E5F), rgba(43, 94, 95, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 12px 32px rgba(43, 94, 95, 0.25), 0 4px 12px rgba(0,0,0,0.1);
    position: relative;
    transition: all 0.3s ease;
}

.module-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(255,255,255,0.2), transparent);
    border-radius: 20px;
    z-index: -1;
}

.module-card:hover .module-icon {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 16px 40px rgba(43, 94, 95, 0.3), 0 6px 16px rgba(0,0,0,0.15);
}

.module-status {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255,255,255,0.9);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #10b981;
    position: relative;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-indicator.active::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: #10b981;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.status-text {
    font-size: 0.8rem;
    font-weight: 600;
    color: #10b981;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.5); opacity: 0.1; }
}

.module-content {
    padding: 0 2.5rem;
    flex: 1;
    position: relative;
    z-index: 2;
}

.module-title {
    font-size: 1.6rem;
    font-weight: 800;
    margin-bottom: 1rem;
    color: #1e293b;
    letter-spacing: -0.8px;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.module-description {
    color: #64748b;
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
    font-weight: 500;
}

.module-footer {
    padding: 2rem 2.5rem 2.5rem;
    border-top: 1px solid rgba(226, 232, 240, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.5), rgba(241, 245, 249, 0.5));
    position: relative;
    z-index: 2;
}

.module-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 2rem;
    right: 2rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(43, 94, 95, 0.1), transparent);
}

.module-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    width: 100%;
}

/* Module action buttons - simplified for professional appearance */
.module-actions .action-btn {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    padding: 0.75rem 1rem;
    background: rgba(43, 94, 95, 0.06);
    border: 1px solid rgba(43, 94, 95, 0.15);
    border-radius: 6px;
    color: #2B5E5F;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    flex: 1;
    min-width: 130px;
    justify-content: center;
    text-decoration: none;
}

.module-actions .action-btn:hover {
    background: rgba(43, 94, 95, 0.12);
    border-color: rgba(43, 94, 95, 0.25);
}

.action-icon {
    font-size: 1.2rem;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

.action-label {
    font-size: 0.8rem;
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.stat-label {
    font-size: 0.7rem;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
    text-align: center;
}

.module-action {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--module-color, #2B5E5F), rgba(43, 94, 95, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0.9;
}

/* No ripple effects */

/* Dashboard Analytics Section */
.dashboard-analytics {
    margin-top: 4rem;
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2.5rem;
    padding: 0 0.5rem;
}

.analytics-header h3 {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    margin: 0;
}

.analytics-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.period-selector {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: #475569;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.period-selector:focus {
    outline: none;
    border-color: #2B5E5F;
    box-shadow: 0 0 0 3px rgba(43, 94, 95, 0.1);
}

.refresh-btn {
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    border: none;
    border-radius: 12px;
    padding: 0.75rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(43, 94, 95, 0.3);
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(43, 94, 95, 0.08), 0 5px 20px rgba(0,0,0,0.04);
    border: 1px solid rgba(255,255,255,0.9);
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    display: flex;
    flex-direction: column;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-color);
}

.stat-card.primary {
    --card-color: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stat-card.warning {
    --card-color: linear-gradient(90deg, #f59e0b, #d97706);
}

.stat-card.success {
    --card-color: linear-gradient(90deg, #10b981, #059669);
}

.stat-card.info {
    --card-color: linear-gradient(90deg, #2B5E5F, #1e4a4b);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 70px rgba(43, 94, 95, 0.12), 0 8px 25px rgba(0,0,0,0.06);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2rem 2rem 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    background: var(--card-color);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-trend.positive {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.stat-trend.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.stat-trend.neutral {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.stat-content {
    padding: 0 2rem 1.5rem;
    flex: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 0.5rem;
    line-height: 1;
    text-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.stat-label {
    color: #475569;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    letter-spacing: -0.2px;
}

.stat-description {
    color: #94a3b8;
    font-size: 0.85rem;
    line-height: 1.4;
}

.stat-progress {
    height: 4px;
    background: rgba(226, 232, 240, 0.5);
    margin: 0 2rem 2rem;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--card-color);
    border-radius: 2px;
    transition: width 1s ease;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Login Footer */
.login-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    text-align: center;
    font-size: 0.875rem;
    color: #666;
}

/* Enhanced Responsive Design */
@media (max-width: 1536px) {
    .header-content {
        max-width: 1200px;
    }

    .main-content {
        max-width: 1200px;
    }
}

@media (max-width: 1280px) {
    .header-content {
        max-width: 1024px;
    }

    .main-content {
        max-width: 1024px;
        padding: 2.5rem 1.5rem 3.5rem;
    }

    .modules-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 1.5rem;
    }

    .analytics-grid {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 1024px) {
    .header-left {
        gap: 2rem;
    }

    .main-navigation {
        display: none;
    }

    .header-content {
        max-width: 768px;
        padding: 1rem 1.5rem;
    }

    .main-content {
        max-width: 768px;
        padding: 2rem 1.5rem 3rem;
    }

    .modules-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .analytics-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 1.5rem;
    }

    .analytics-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .analytics-controls {
        align-self: stretch;
        justify-content: space-between;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1rem;
        min-height: auto;
    }

    .header-left {
        width: 100%;
        justify-content: center;
    }

    .header-right {
        width: 100%;
        justify-content: center;
    }

    .brand-text h1 {
        font-size: 1.4rem;
    }

    .user-profile {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .user-actions {
        margin-left: 0;
        padding-left: 0;
        border-left: none;
        border-top: 1px solid rgba(255,255,255,0.2);
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .main-content {
        padding: 1.5rem 1rem 2.5rem;
    }

    .welcome-section {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }

    .welcome-section h2 {
        font-size: 1.75rem;
    }

    .modules-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .module-card {
        min-height: 240px;
    }

    .module-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .module-content {
        padding: 0 1.5rem;
    }

    .module-footer {
        padding: 1rem 1.5rem 1.5rem;
    }

    .module-stats {
        gap: 1rem;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stat-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .stat-content {
        padding: 0 1.5rem 1rem;
    }

    .stat-progress {
        margin: 0 1.5rem 1.5rem;
    }

    .analytics-header h3 {
        font-size: 1.75rem;
    }
}

@media (max-width: 640px) {
    .header-content {
        padding: 0.75rem;
    }

    .brand-section {
        gap: 0.75rem;
    }

    .brand-text h1 {
        font-size: 1.25rem;
    }

    .brand-subtitle {
        font-size: 0.7rem;
    }

    .header-actions {
        gap: 0.75rem;
    }

    .action-btn {
        padding: 0.6rem;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    .main-content {
        padding: 1rem 0.75rem 2rem;
    }

    .welcome-section {
        padding: 1.5rem;
        border-radius: 16px;
    }

    .welcome-section h2 {
        font-size: 1.5rem;
    }

    .module-card {
        border-radius: 16px;
        min-height: 220px;
    }

    .module-header {
        padding: 1.25rem 1.25rem 0.75rem;
    }

    .module-icon {
        width: 56px;
        height: 56px;
    }

    .module-content {
        padding: 0 1.25rem;
    }

    .module-title {
        font-size: 1.25rem;
    }

    .module-description {
        font-size: 0.9rem;
    }

    .module-footer {
        padding: 0.75rem 1.25rem 1.25rem;
    }

    .stat-card {
        border-radius: 16px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .analytics-header h3 {
        font-size: 1.5rem;
    }

    .period-selector {
        font-size: 0.85rem;
        padding: 0.6rem 0.8rem;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 0.75rem 0.5rem 1.5rem;
    }

    .welcome-section {
        padding: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .welcome-section h2 {
        font-size: 1.375rem;
    }

    .modules-grid {
        gap: 1.25rem;
    }

    .analytics-grid {
        gap: 1.25rem;
    }

    .analytics-header {
        margin-bottom: 2rem;
    }

    .analytics-controls {
        flex-direction: column;
        gap: 0.75rem;
    }
}

/* Advanced Animations & Micro-interactions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Simplified animations - only keep essential ones */

/* Loading States */
.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Enhanced Hover Effects */
.interactive-element {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-2px);
}

.interactive-element:active {
    transform: translateY(0);
    transition: all 0.1s;
}

/* No staggered animations */

/* Module cards - no animations */

.stat-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.stat-card:nth-child(1) { animation-delay: 0.6s; }
.stat-card:nth-child(2) { animation-delay: 0.7s; }
.stat-card:nth-child(3) { animation-delay: 0.8s; }
.stat-card:nth-child(4) { animation-delay: 0.9s; }

/* Advanced Button Interactions */
.btn {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* Enhanced Focus States */
.btn:focus,
.nav-item:focus,
.action-btn:focus,
.period-selector:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(43, 94, 95, 0.2);
}

/* Icons - no floating animation */

/* No glow effects */

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Simple Transitions */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.spinner-ring {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(43, 94, 95, 0.1);
    border-top: 4px solid #2B5E5F;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #2B5E5F;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(226, 232, 240, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1e4a4b, #2B5E5F);
}

/* Selection Styling */
::selection {
    background: rgba(43, 94, 95, 0.2);
    color: #1e293b;
}

::-moz-selection {
    background: rgba(43, 94, 95, 0.2);
    color: #1e293b;
}

/* Focus Visible Support */
.btn:focus-visible,
.nav-item:focus-visible,
.action-btn:focus-visible {
    outline: 2px solid #2B5E5F;
    outline-offset: 2px;
}

/* Invoice Cards Styling */
.invoices-container {
    margin-top: 2rem;
}

.invoices-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 0 0.5rem;
}

.invoices-header h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.invoices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
}

.invoice-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(43, 94, 95, 0.08), 0 5px 20px rgba(0,0,0,0.04);
    border: 1px solid rgba(255,255,255,0.9);
    overflow: hidden;
    transition: all 0.4s ease;
    position: relative;
}

.invoice-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--status-color, #2B5E5F);
}

.invoice-card[data-status="paid"]::before,
.invoice-card[data-status="completed"]::before {
    background: linear-gradient(90deg, #10b981, #059669);
}

.invoice-card[data-status="pending"]::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.invoice-card[data-status="overdue"]::before {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.invoice-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 70px rgba(43, 94, 95, 0.12), 0 8px 25px rgba(0,0,0,0.06);
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2rem 2rem 1rem;
}

.invoice-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 8px 20px rgba(43, 94, 95, 0.2);
}

.invoice-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge.status-success {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.status-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.status-secondary {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.invoice-content {
    padding: 0 2rem 1.5rem;
}

.invoice-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.invoice-description {
    color: #64748b;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

.invoice-details {
    display: grid;
    gap: 0.75rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    color: #94a3b8;
    font-size: 0.85rem;
    font-weight: 500;
}

.detail-value {
    color: #475569;
    font-weight: 600;
    font-size: 0.9rem;
}

.detail-value.amount {
    color: #2B5E5F;
    font-size: 1.1rem;
    font-weight: 700;
}

.detail-value.due-date {
    color: #f59e0b;
}

.invoice-actions {
    padding: 1.5rem 2rem 2rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    gap: 1rem;
}

.btn-icon {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    justify-content: center;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    transform: translateY(-2px);
}

.btn-icon.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Invoice Table Styling */
.invoices-table-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(43, 94, 95, 0.08), 0 5px 20px rgba(0,0,0,0.04);
    border: 1px solid rgba(255,255,255,0.9);
    overflow: hidden;
    margin-top: 2rem;
    overflow-x: auto;
}

.invoices-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
    min-width: 800px;
    table-layout: fixed;
}

.invoices-table thead {
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    color: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

.invoices-table th {
    padding: 1.25rem 1rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid rgba(255,255,255,0.2);
    white-space: nowrap;
}

/* Column widths */
.invoices-table th:nth-child(1) { width: 15%; } /* Invoice Number */
.invoices-table th:nth-child(2) { width: 25%; } /* Description */
.invoices-table th:nth-child(3) { width: 12%; } /* Amount */
.invoices-table th:nth-child(4) { width: 10%; } /* Date */
.invoices-table th:nth-child(5) { width: 10%; } /* Due Date */
.invoices-table th:nth-child(6) { width: 10%; } /* Status */
.invoices-table th:nth-child(7) { width: 9%; }  /* Download PDF */
.invoices-table th:nth-child(8) { width: 9%; }  /* Payment */

.invoices-table tbody tr {
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    transition: background-color 0.2s ease;
}

.invoices-table tbody tr:hover {
    background: rgba(43, 94, 95, 0.02);
}

.invoices-table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

.invoices-table tbody tr:nth-child(even):hover {
    background: rgba(43, 94, 95, 0.03);
}

.invoices-table td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
    border-right: 1px solid rgba(226, 232, 240, 0.3);
}

.invoices-table td:last-child {
    border-right: none;
}

/* Table Cell Styling */
.invoice-number {
    color: #2B5E5F;
    font-weight: 700;
    font-size: 1rem;
    font-family: 'Courier New', monospace;
}

.invoice-description {
    color: #475569;
    line-height: 1.4;
    font-size: 0.9rem;
    word-wrap: break-word;
}

.invoice-amount {
    color: #2B5E5F;
    font-weight: 700;
    font-size: 1.1rem;
    text-align: right;
    font-family: 'Courier New', monospace;
}

.invoice-date,
.invoice-due-date {
    color: #475569;
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
}

.invoice-due-date {
    color: #f59e0b;
    font-weight: 600;
}

.invoice-status {
    text-align: center;
}

.invoice-status .status-badge {
    padding: 0.5rem 0.8rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    white-space: nowrap;
    min-width: 80px;
    justify-content: center;
}

.invoice-download,
.invoice-payment {
    text-align: center;
    vertical-align: middle;
}

.invoice-download .btn,
.invoice-payment .btn {
    font-size: 0.8rem;
    padding: 0.5rem 0.8rem;
    border-radius: 8px;
    font-weight: 500;
    min-width: 90px;
    text-align: center;
    white-space: nowrap;
    display: inline-block;
}

/* Status Badge Colors */
.status-badge.status-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.status-badge.status-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.status-badge.status-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.status-badge.status-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

/* Button Styling */
.btn-sm {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563, #374151);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
}

.btn-primary {
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    color: white;
    box-shadow: 0 2px 8px rgba(43, 94, 95, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e4a4b, #163a3b);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(43, 94, 95, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Responsive Table */
@media (max-width: 1200px) {
    .invoices-table-container {
        overflow-x: auto;
    }

    .invoices-table {
        min-width: 1000px;
    }

    .invoice-download .btn,
    .invoice-payment .btn {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
        min-width: 80px;
    }
}

@media (max-width: 768px) {
    .invoices-table {
        min-width: 900px;
    }

    .invoices-table th,
    .invoices-table td {
        padding: 1rem 0.5rem;
    }

    .invoice-download .btn,
    .invoice-payment .btn {
        font-size: 0.7rem;
        padding: 0.3rem 0.5rem;
        min-width: 70px;
    }

    /* Adjust column widths for mobile */
    .invoices-table th:nth-child(1) { width: 12%; } /* Invoice Number */
    .invoices-table th:nth-child(2) { width: 30%; } /* Description */
    .invoices-table th:nth-child(3) { width: 10%; } /* Amount */
    .invoices-table th:nth-child(4) { width: 8%; }  /* Date */
    .invoices-table th:nth-child(5) { width: 8%; }  /* Due Date */
    .invoices-table th:nth-child(6) { width: 10%; } /* Status */
    .invoices-table th:nth-child(7) { width: 11%; } /* Download PDF */
    .invoices-table th:nth-child(8) { width: 11%; } /* Payment */
}

/* Print Styles */
@media print {
    .header,
    .loading-overlay,
    .action-btn,
    .refresh-btn {
        display: none !important;
    }

    .main-content {
        max-width: none;
        padding: 0;
        margin: 0;
    }

    .module-card,
    .stat-card,
    .invoice-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #e2e8f0;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .loading-overlay {
        display: none !important;
    }
}

/* Admin Styles */
.form-section {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.role-badge, .status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
}

.role-admin {
    background-color: #dc3545;
    color: white;
}

.role-user {
    background-color: #28a745;
    color: white;
}

.status-active {
    background-color: #28a745;
    color: white;
}

.status-inactive {
    background-color: #6c757d;
    color: white;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
