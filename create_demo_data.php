<?php
/**
 * Demo Data Generator
 * Creates sample PDF files for testing the FTP portal
 */

// Create demo directory structure
$demo_base = __DIR__ . '/demo_ftp_data';
$users = ['client1', 'client2', 'testuser', 'demo'];

echo "<h1>Demo Data Generator</h1>\n";

// Create base directory
if (!is_dir($demo_base)) {
    mkdir($demo_base, 0755, true);
    echo "Created base directory: $demo_base<br>\n";
}

// Create invoices directory
$invoices_dir = $demo_base . '/invoices';
if (!is_dir($invoices_dir)) {
    mkdir($invoices_dir, 0755, true);
    echo "Created invoices directory: $invoices_dir<br>\n";
}

// Create user directories and sample PDF files
foreach ($users as $user) {
    $user_dir = $invoices_dir . '/' . $user;
    
    if (!is_dir($user_dir)) {
        mkdir($user_dir, 0755, true);
        echo "Created user directory: $user_dir<br>\n";
    }
    
    // Create sample PDF files
    for ($i = 1; $i <= 3; $i++) {
        $filename = "invoice_" . date('Y') . "_" . str_pad($i, 3, '0', STR_PAD_LEFT) . ".pdf";
        $filepath = $user_dir . '/' . $filename;
        
        if (!file_exists($filepath)) {
            // Create a simple PDF content
            $pdf_content = generateSimplePDF($user, $filename, $i);
            file_put_contents($filepath, $pdf_content);
            echo "Created sample invoice: $filepath<br>\n";
        }
    }
}

echo "<h2>Demo FTP Structure Created</h2>\n";
echo "<p>You can now copy the contents of <code>$demo_base</code> to your FTP server.</p>\n";
echo "<h3>Test Credentials</h3>\n";
echo "<ul>\n";
foreach ($users as $user) {
    echo "<li>Username: <strong>$user</strong> | Password: <strong>password123</strong></li>\n";
}
echo "</ul>\n";

echo "<h3>Directory Structure</h3>\n";
echo "<pre>\n";
showDirectoryStructure($demo_base);
echo "</pre>\n";

echo "<p><a href='test_ftp_auth.php'>Run FTP Authentication Test</a></p>\n";
echo "<p><a href='login.php'>Go to Login Page</a></p>\n";

/**
 * Generate a simple PDF file content
 */
function generateSimplePDF($user, $filename, $invoice_number) {
    $amount = number_format(100 + ($invoice_number * 50) + (crc32($user) % 500), 2);
    $date = date('Y-m-d', strtotime("-" . ($invoice_number * 30) . " days"));
    
    $pdf_content = "%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj
4 0 obj
<<
/Length 200
>>
stream
BT
/F1 12 Tf
50 750 Td
(COMMERCIAL INVOICE) Tj
0 -30 Td
(Invoice: $filename) Tj
0 -20 Td
(Client: $user) Tj
0 -20 Td
(Date: $date) Tj
0 -20 Td
(Amount: \$$amount) Tj
0 -30 Td
(Status: PENDING PAYMENT) Tj
ET
endstream
endobj
5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000273 00000 n 
0000000525 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
622
%%EOF";
    
    return $pdf_content;
}

/**
 * Show directory structure
 */
function showDirectoryStructure($dir, $prefix = '') {
    $items = scandir($dir);
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        
        $path = $dir . '/' . $item;
        echo $prefix . $item;
        
        if (is_dir($path)) {
            echo "/\n";
            showDirectoryStructure($path, $prefix . '  ');
        } else {
            echo " (" . number_format(filesize($path)) . " bytes)\n";
        }
    }
}
?>
