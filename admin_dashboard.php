<?php
/**
 * Admin Dashboard
 * Comprehensive administrative interface for system management
 */

require_once 'config/config.php';

// Require admin access
requireLogin();
if (!isAdmin()) {
    header('Location: dashboard.php');
    exit();
}

// Initialize permissions and get admin data
initializeUserPermissions();
$system_stats = getSystemPaymentStats();
$system_activity = getSystemActivity();
$all_users = getAllDemoUsers();
$all_invoices = getAllUsersInvoices();
$module_usage_stats = getModuleUsageStats();
$permissions_matrix = getPermissionsMatrix();

// Handle search and filters
$search_query = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$filter_user = isset($_GET['user']) ? sanitizeInput($_GET['user']) : '';
$filter_status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';

// Filter invoices based on search criteria
$filtered_invoices = $all_invoices;
if ($search_query) {
    $filtered_invoices = array_filter($filtered_invoices, function($invoice) use ($search_query) {
        return stripos($invoice['filename'], $search_query) !== false ||
               stripos($invoice['username'], $search_query) !== false;
    });
}
if ($filter_user) {
    $filtered_invoices = array_filter($filtered_invoices, function($invoice) use ($filter_user) {
        return $invoice['username'] === $filter_user;
    });
}
if ($filter_status) {
    $filtered_invoices = array_filter($filtered_invoices, function($invoice) use ($filter_status) {
        return $invoice['payment_status'] === $filter_status;
    });
}

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #2B5E5F 0%, #3b82f6 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .admin-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .admin-badge {
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #2B5E5F;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2B5E5F;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #6b7280;
            font-size: 0.9em;
        }
        .admin-section {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f3f4f6;
        }
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2B5E5F;
        }
        .filter-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .filter-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .admin-table th,
        .admin-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .admin-table th {
            background: #f9fafb;
            font-weight: bold;
            color: #374151;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-paid {
            background: #d1fae5;
            color: #065f46;
        }
        .status-unpaid {
            background: #fee2e2;
            color: #991b1b;
        }
        .user-badge {
            background: #e0e7ff;
            color: #3730a3;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8em;
        }
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .btn-admin {
            background: #2B5E5F;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9em;
            cursor: pointer;
        }
        .btn-admin:hover {
            background: #1e4344;
        }
        .recent-activity {
            max-height: 300px;
            overflow-y: auto;
        }
        .activity-item {
            padding: 10px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .activity-time {
            color: #6b7280;
            font-size: 0.8em;
        }
    </style>
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-logo">
                        <img src="assets/img/logo.png" alt="<?php echo APP_NAME; ?>" class="logo-image">
                    </div>
                    <div class="brand-text">
                        <span class="brand-subtitle">Admin Portal</span>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-menu-container">
                    <button class="user-menu-trigger" onclick="toggleSettings()">
                        <div class="user-avatar-small">
                            <span><?php echo strtoupper(substr($_SESSION['ftp_username'], 0, 2)); ?></span>
                        </div>
                        <div class="user-info-compact">
                            <div class="user-name-compact"><?php echo htmlspecialchars($_SESSION['ftp_username']); ?></div>
                            <div class="user-role-compact">Administrator</div>
                        </div>
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor" class="dropdown-chevron">
                            <path d="M2 4l4 4 4-4H2z"/>
                        </svg>
                    </button>
                    
                    <div class="user-menu-dropdown" id="settingsDropdown">
                        <div class="user-menu-header">
                            <div class="user-profile-info">
                                <div class="user-name-full"><?php echo htmlspecialchars($_SESSION['ftp_username']); ?></div>
                                <div class="user-details-full">
                                    <span class="client-name">System Administrator</span>
                                    <span class="role-badge-small">Admin</span>
                                </div>
                            </div>
                        </div>
                        <div class="menu-divider"></div>
                        <a href="dashboard.php" class="user-menu-item">
                            User Dashboard
                        </a>
                        <a href="admin_permissions.php" class="user-menu-item">
                            Module Permissions
                        </a>
                        <a href="admin_user_management.php" class="user-menu-item">
                            User Management
                        </a>
                        <a href="demo_status.php" class="user-menu-item">
                            Demo Status
                        </a>
                        <div class="menu-divider"></div>
                        <a href="logout.php" class="user-menu-item logout-item">
                            Sign Out
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="admin-header">
            <div class="container">
                <div class="admin-title">
                    <h1>🛡️ System Administration</h1>
                    <span class="admin-badge">ADMIN ACCESS</span>
                </div>
                <p>Comprehensive system management and monitoring</p>
            </div>
        </div>

        <div class="container">
            <!-- System Overview Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value"><?php echo $system_activity['total_users']; ?></div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$<?php echo number_format($system_stats['total_amount'], 2); ?></div>
                    <div class="stat-label">Total Payments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value"><?php echo $system_stats['pending_payments']; ?></div>
                    <div class="stat-label">Pending Payments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value"><?php echo $system_activity['total_invoices']; ?></div>
                    <div class="stat-label">Total Invoices</div>
                </div>
            </div>

            <!-- Module Management Section -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">🧩 Module Management</h2>
                    <a href="admin_permissions.php" class="btn-admin">Manage Permissions</a>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <?php foreach (AVAILABLE_MODULES as $module_key => $module_info): ?>
                    <div class="module-card" style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-left: 4px solid #2B5E5F;">
                        <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                            <div style="font-size: 2em;"><?php echo $module_info['icon']; ?></div>
                            <div>
                                <h3 style="margin: 0; color: #2B5E5F;"><?php echo htmlspecialchars($module_info['name']); ?></h3>
                                <p style="margin: 5px 0 0 0; color: #6b7280; font-size: 0.9em;"><?php echo htmlspecialchars($module_info['description']); ?></p>
                            </div>
                        </div>

                        <div style="background: #f0f9ff; padding: 10px; border-radius: 5px; margin-bottom: 15px; border-left: 3px solid #2B5E5F;">
                            <div style="font-size: 1.5em; font-weight: bold; color: #2B5E5F;">
                                <?php echo isset($module_usage_stats[$module_key]) ? $module_usage_stats[$module_key] : 0; ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.8em;">Users with access</div>
                        </div>

                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <?php foreach ($module_info['admin_actions'] as $action): ?>
                            <button class="btn-admin" style="font-size: 0.8em; padding: 5px 10px; background: #6b7280;">
                                <?php echo htmlspecialchars($action); ?>
                            </button>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Permissions Overview Table -->
                <h3 style="margin-bottom: 15px; color: #374151;">Module Access Overview</h3>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <?php foreach (AVAILABLE_MODULES as $module_key => $module_info): ?>
                            <th style="text-align: center;"><?php echo $module_info['icon']; ?><br><small><?php echo $module_info['name']; ?></small></th>
                            <?php endforeach; ?>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($permissions_matrix as $username => $user_permissions): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($username); ?></strong><br>
                                <small style="color: #6b7280;">
                                    <?php echo array_sum($user_permissions); ?> of <?php echo count(AVAILABLE_MODULES); ?> modules
                                </small>
                            </td>
                            <?php foreach (AVAILABLE_MODULES as $module_key => $module_info): ?>
                            <td style="text-align: center;">
                                <?php if ($user_permissions[$module_key]): ?>
                                    <span style="color: #22c55e; font-size: 1.2em;">✓</span>
                                <?php else: ?>
                                    <span style="color: #ef4444; font-size: 1.2em;">✗</span>
                                <?php endif; ?>
                            </td>
                            <?php endforeach; ?>
                            <td>
                                <a href="admin_permissions.php?user=<?php echo urlencode($username); ?>"
                                   class="btn-admin" style="font-size: 0.8em; padding: 5px 10px;">
                                    Edit Permissions
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- All Users' Invoices Management -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">📄 All Users' Invoices</h2>
                    <span><?php echo count($filtered_invoices); ?> invoices</span>
                </div>

                <div class="filter-controls">
                    <form method="GET" style="display: flex; gap: 15px; flex-wrap: wrap;">
                        <input type="text" name="search" placeholder="Search invoices..."
                               value="<?php echo htmlspecialchars($search_query); ?>" class="filter-input">

                        <select name="user" class="filter-input">
                            <option value="">All Users</option>
                            <?php foreach (array_keys($all_users) as $username): ?>
                                <?php if ($username !== 'admin'): ?>
                                    <option value="<?php echo $username; ?>" <?php echo $filter_user === $username ? 'selected' : ''; ?>>
                                        <?php echo ucfirst($username); ?>
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>

                        <select name="status" class="filter-input">
                            <option value="">All Status</option>
                            <option value="paid" <?php echo $filter_status === 'paid' ? 'selected' : ''; ?>>Paid</option>
                            <option value="unpaid" <?php echo $filter_status === 'unpaid' ? 'selected' : ''; ?>>Unpaid</option>
                        </select>

                        <button type="submit" class="btn-admin">Filter</button>
                        <a href="admin_dashboard.php" class="btn-admin" style="background: #6b7280;">Clear</a>
                    </form>
                </div>

                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Invoice</th>
                            <th>User</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($filtered_invoices, 0, 20) as $invoice): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($invoice['filename']); ?></strong><br>
                                <small style="color: #6b7280;"><?php echo number_format($invoice['size']); ?> bytes</small>
                            </td>
                            <td>
                                <span class="user-badge"><?php echo htmlspecialchars($invoice['username']); ?></span>
                            </td>
                            <td>
                                <strong>$<?php echo number_format($invoice['amount'], 2); ?></strong>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $invoice['payment_status']; ?>">
                                    <?php echo ucfirst($invoice['payment_status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo date('Y-m-d H:i', $invoice['modified']); ?>
                            </td>
                            <td>
                                <a href="modules/invoices.php?download=<?php echo urlencode($invoice['filename']); ?>"
                                   class="btn-admin" style="font-size: 0.8em; padding: 5px 10px;">
                                    Download
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>

                        <?php if (empty($filtered_invoices)): ?>
                        <tr>
                            <td colspan="6" style="text-align: center; color: #6b7280; padding: 30px;">
                                No invoices found matching your criteria.
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Payment Monitoring -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">💳 Payment Monitoring</h2>
                    <span><?php echo $system_stats['total_payments']; ?> total payments</span>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; border-left: 4px solid #22c55e;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #15803d;">
                            $<?php echo number_format($system_stats['total_amount'], 2); ?>
                        </div>
                        <div style="color: #16a34a;">Total Revenue</div>
                    </div>
                    <div style="background: #fef2f2; padding: 15px; border-radius: 8px; border-left: 4px solid #ef4444;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #dc2626;">
                            $<?php echo number_format($system_stats['pending_amount'], 2); ?>
                        </div>
                        <div style="color: #dc2626;">Outstanding Balance</div>
                    </div>
                </div>

                <?php if (!empty($system_stats['recent_payments'])): ?>
                <h3 style="margin-bottom: 15px; color: #374151;">Recent Payments</h3>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Reference</th>
                            <th>Invoice</th>
                            <th>User</th>
                            <th>Amount</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($system_stats['recent_payments'], 0, 10) as $payment): ?>
                        <tr>
                            <td>
                                <code style="background: #f3f4f6; padding: 2px 6px; border-radius: 3px;">
                                    <?php echo htmlspecialchars($payment['payment_reference']); ?>
                                </code>
                            </td>
                            <td><?php echo htmlspecialchars($payment['invoice_name']); ?></td>
                            <td>
                                <span class="user-badge"><?php echo htmlspecialchars($payment['user']); ?></span>
                            </td>
                            <td><strong>$<?php echo number_format($payment['amount'], 2); ?></strong></td>
                            <td><?php echo $payment['payment_date']; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <div style="text-align: center; color: #6b7280; padding: 30px;">
                    No payments recorded yet.
                </div>
                <?php endif; ?>
            </div>

            <!-- User Management -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">👥 User Management</h2>
                    <span><?php echo count($all_users); ?> users</span>
                </div>

                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Status</th>
                            <th>Invoices</th>
                            <th>Payments</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($all_users as $username => $user_data): ?>
                        <?php if ($username !== 'admin'): ?>
                        <?php
                            $user_invoices = array_filter($all_invoices, function($inv) use ($username) {
                                return $inv['username'] === $username;
                            });
                            $user_payments = 0;
                            if (isset($_SESSION['demo_payments'])) {
                                foreach ($_SESSION['demo_payments'] as $payment) {
                                    if ($payment['user'] === $username) {
                                        $user_payments++;
                                    }
                                }
                            }
                        ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($username); ?></strong><br>
                                <small style="color: #6b7280;">FTP User</small>
                            </td>
                            <td>
                                <span class="status-badge" style="background: #d1fae5; color: #065f46;">
                                    <?php echo ucfirst($user_data['status']); ?>
                                </span>
                            </td>
                            <td><?php echo count($user_invoices); ?> invoices</td>
                            <td><?php echo $user_payments; ?> payments</td>
                            <td><?php echo date('Y-m-d H:i', $user_data['last_login']); ?></td>
                            <td>
                                <a href="admin_user_details.php?user=<?php echo urlencode($username); ?>"
                                   class="btn-admin" style="font-size: 0.8em; padding: 5px 10px;">
                                    View Details
                                </a>
                            </td>
                        </tr>
                        <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- System Administration -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">⚙️ System Administration</h2>
                    <span>System Status</span>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #0ea5e9;">
                        <h4 style="margin: 0 0 10px 0; color: #0c4a6e;">Demo Mode Status</h4>
                        <div style="font-size: 1.2em; font-weight: bold; color: #0369a1;">
                            <?php echo FTP_DEMO_MODE ? '🟢 ENABLED' : '🔴 DISABLED'; ?>
                        </div>
                        <p style="margin: 5px 0 0 0; color: #0369a1; font-size: 0.9em;">
                            <?php echo FTP_DEMO_MODE ? 'Running in demo mode for development' : 'Production mode active'; ?>
                        </p>
                    </div>

                    <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; border-left: 4px solid #22c55e;">
                        <h4 style="margin: 0 0 10px 0; color: #14532d;">FTP Connection</h4>
                        <div style="font-size: 1.2em; font-weight: bold; color: #15803d;">
                            <?php echo FTP_DEMO_MODE ? '🟢 SIMULATED' : '🟡 CHECKING...'; ?>
                        </div>
                        <p style="margin: 5px 0 0 0; color: #15803d; font-size: 0.9em;">
                            Host: <?php echo FTP_HOST; ?>:<?php echo FTP_PORT; ?>
                        </p>
                    </div>

                    <div style="background: #fefce8; padding: 20px; border-radius: 8px; border-left: 4px solid #eab308;">
                        <h4 style="margin: 0 0 10px 0; color: #713f12;">System Uptime</h4>
                        <div style="font-size: 1.2em; font-weight: bold; color: #a16207;">
                            <?php echo $system_activity['system_uptime']; ?>
                        </div>
                        <p style="margin: 5px 0 0 0; color: #a16207; font-size: 0.9em;">
                            Last restart: <?php echo date('Y-m-d H:i', time() - 86400); ?>
                        </p>
                    </div>
                </div>

                <div class="quick-actions">
                    <a href="demo_status.php" class="btn-admin">View Demo Status</a>
                    <a href="test_demo_system.php" class="btn-admin">Run System Tests</a>
                    <a href="dashboard.php" class="btn-admin" style="background: #6b7280;">Switch to User View</a>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">📊 Recent Activity</h2>
                    <span>Last 24 hours</span>
                </div>

                <div class="recent-activity">
                    <?php
                    $recent_activities = [
                        ['action' => 'User login', 'user' => 'demo', 'time' => time() - 300],
                        ['action' => 'Payment processed', 'user' => 'client1', 'time' => time() - 1800],
                        ['action' => 'Invoice downloaded', 'user' => 'client2', 'time' => time() - 3600],
                        ['action' => 'User login', 'user' => 'testuser', 'time' => time() - 7200],
                        ['action' => 'System backup', 'user' => 'system', 'time' => time() - 14400]
                    ];
                    ?>

                    <?php foreach ($recent_activities as $activity): ?>
                    <div class="activity-item">
                        <div>
                            <strong><?php echo htmlspecialchars($activity['action']); ?></strong>
                            <?php if ($activity['user'] !== 'system'): ?>
                                by <span class="user-badge"><?php echo htmlspecialchars($activity['user']); ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="activity-time">
                            <?php echo date('H:i', $activity['time']); ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </main>

    <script>
        function toggleSettings() {
            const dropdown = document.getElementById('settingsDropdown');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('settingsDropdown');
            const trigger = document.querySelector('.user-menu-trigger');

            if (!trigger.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
    </script>
</body>
</html>
