# FTP-Based Portal Application

A secure PHP web application that replaces FTP File Explorer access with a browser-based interface. Users authenticate directly with their FTP credentials and access their files through a modern web interface.

## Key Features

- **FTP-Based Authentication**: No database required - users login with their FTP credentials
- **Multi-Tenant Structure**: Each user has their own FTP folder (`/invoices/{username}/`)
- **Secure File Operations**: Download PDF invoices directly from FTP
- **Payment Integration**: Process payments and upload confirmation files to FTP
- **Modern Dashboard**: Professional interface with module-based navigation
- **Security First**: Path sanitization, input validation, and secure session management

## Requirements

- PHP 7.4 or higher
- FTP server with user accounts
- Web server (Apache/Nginx)
- FTP extension enabled in PHP

## Installation

1. **Upload Files**: Copy all files to your web server directory

2. **Configure FTP Settings**: Edit `config/config.php`:
   ```php
   define('FTP_HOST', 'your-ftp-server.com');
   define('FTP_PORT', 21);
   define('FTP_BASE_PATH', '/invoices/');
   ```

3. **Set Up FTP Structure**: Create the following folder structure on your FTP server:
   ```
   /invoices/
   ├── user1/
   │   ├── invoice1.pdf
   │   └── invoice2.pdf
   ├── user2/
   │   ├── invoice3.pdf
   │   └── invoice4.pdf
   └── ...
   ```

4. **Configure Payment Gateway**: Update PayFast settings in `config/config.php`:
   ```php
   define('PAYFAST_MERCHANT_ID', 'your_merchant_id');
   define('PAYFAST_MERCHANT_KEY', 'your_merchant_key');
   define('PAYFAST_PASSPHRASE', 'your_passphrase');
   ```

5. **Set Permissions**: Ensure web server can write to temporary directories

## Usage

### For Users

1. **Login**: Use your FTP username and password
2. **View Invoices**: Browse PDF files in your folder
3. **Download Files**: Click download button for any invoice
4. **Make Payments**: Click "Pay Now" to process payments
5. **Access Modules**: Use dashboard to access different system modules

### For Administrators

1. **User Management**: Users are managed through FTP server accounts
2. **File Management**: Upload invoices to user folders via FTP
3. **Payment Tracking**: Check for `*_PAID_*.txt` files in user folders

## Security Features

- **Path Sanitization**: Prevents directory traversal attacks
- **Input Validation**: All user inputs are sanitized
- **Session Security**: Secure session handling with timeouts
- **File Type Validation**: Only PDF files are accessible
- **FTP Credential Protection**: Credentials stored securely in sessions
- **Security Logging**: All security events are logged

## Module Overview

- **Invoices**: View and download PDF invoices, process payments
- **Freight Management**: Advanced logistics coordination
- **Tariff Book**: Customs tariff and HS code lookup
- **Trade Finance**: Financial management for international trade
- **Official Website**: Link to main company website

## Testing

Run the test file to verify installation:
```
http://your-domain.com/test_ftp_auth.php
```

## File Structure

```
portal-dev/
├── config/
│   └── config.php          # Main configuration
├── modules/
│   ├── invoices.php        # Invoice management
│   ├── payment.php         # Payment processing
│   ├── freight.php         # Freight module
│   ├── tariff.php          # Tariff module
│   ├── accounting.php      # Accounting module
│   └── backoffice.php      # Website redirect
├── classes/
│   └── FTPManager.php      # FTP operations
├── assets/                 # CSS, JS, images
├── api/                    # API endpoints
├── login.php               # Login page
├── dashboard.php           # Main dashboard
├── logout.php              # Logout handler
└── index.php               # Entry point
```

## Troubleshooting

1. **FTP Connection Issues**: Check FTP server settings and credentials
2. **File Download Problems**: Verify file permissions and FTP passive mode
3. **Session Issues**: Check PHP session configuration
4. **Payment Problems**: Verify PayFast configuration

## Security Notes

- Change default FTP credentials before production use
- Use HTTPS in production
- Regularly monitor security logs
- Keep PHP and server software updated
- Consider using SFTP for enhanced security

## Support

For issues or questions, check the error logs and security event logs for detailed information.
