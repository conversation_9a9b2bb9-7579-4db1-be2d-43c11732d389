-- Portal Application Database Schema

CREATE DATABASE IF NOT EXISTS portal_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE portal_app;

-- Clients table (tenants)
CREATE TABLE clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    ftp_folder_path VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active'
);

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    client_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    INDEX idx_email (email),
    INDEX idx_client_id (client_id)
);

-- User rights table
CREATE TABLE user_rights (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    module_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_module (user_id, module_name),
    INDEX idx_user_id (user_id)
);

-- Payments table
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_name VARCHAR(255) NOT NULL,
    user_id INT NOT NULL,
    client_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    payment_reference VARCHAR(255),
    payment_gateway VARCHAR(50) DEFAULT 'payfast',
    gateway_transaction_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_client_id (client_id),
    INDEX idx_payment_status (payment_status)
);

-- Sessions table for session management
CREATE TABLE user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
);

-- Insert default admin user and client
INSERT INTO clients (name, ftp_folder_path) VALUES 
('Default Client', '/invoices/default/'),
('Demo Client', '/invoices/demo/');

-- Default admin user (password: admin123 - change this!)
INSERT INTO users (name, email, password, role, client_id) VALUES 
('System Admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1);

-- Grant all rights to admin
INSERT INTO user_rights (user_id, module_name) VALUES 
(1, 'invoices'),
(1, 'freight'),
(1, 'tariff'),
(1, 'accounting'),
(1, 'backoffice');

-- Demo user
INSERT INTO users (name, email, password, role, client_id) VALUES 
('Demo User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 2);

-- Grant limited rights to demo user
INSERT INTO user_rights (user_id, module_name) VALUES 
(2, 'invoices'),
(2, 'accounting');
