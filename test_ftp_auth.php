<?php
/**
 * FTP Authentication Test
 * Test the FTP-based authentication system
 */

require_once 'config/config.php';

echo "<h1>FTP Authentication Test</h1>\n";

// Test 1: Test FTP connection function
echo "<h2>Test 1: FTP Connection Function</h2>\n";
try {
    // Test with dummy credentials (will fail but should not crash)
    $result = authenticateFTP('testuser', 'testpass');
    echo "Authentication result: " . ($result ? 'SUCCESS' : 'FAILED') . "<br>\n";
} catch (Exception $e) {
    echo "Exception caught: " . $e->getMessage() . "<br>\n";
}

// Test 2: Test username sanitization
echo "<h2>Test 2: Username Sanitization</h2>\n";
$test_usernames = [
    'validuser',
    'user123',
    'user../../../etc/passwd',
    'user\\windows\\system32',
    'user:with:colons',
    'user with spaces',
    'UPPERCASE_USER'
];

foreach ($test_usernames as $username) {
    $sanitized = sanitizeFTPUsername($username);
    echo "Original: '$username' -> Sanitized: '$sanitized'<br>\n";
}

// Test 3: Test file path validation
echo "<h2>Test 3: File Path Validation</h2>\n";
$test_files = [
    'invoice.pdf',
    'invoice123.pdf',
    '../../../etc/passwd',
    'invoice.exe',
    'very_long_filename_that_exceeds_normal_limits_and_should_be_rejected_because_it_is_too_long_for_normal_filesystem_operations_and_could_cause_buffer_overflows_or_other_security_issues_in_poorly_written_applications.pdf',
    'invoice with spaces.pdf',
    'invoice.PDF'
];

foreach ($test_files as $filename) {
    $validated = validateFilePath($filename);
    echo "File: '$filename' -> Valid: " . ($validated ? $validated : 'INVALID') . "<br>\n";
}

// Test 4: Test session functions
echo "<h2>Test 4: Session Functions</h2>\n";
echo "Is logged in: " . (isLoggedIn() ? 'YES' : 'NO') . "<br>\n";
echo "User FTP path: " . (getUserFTPPath() ?: 'NOT SET') . "<br>\n";

// Test 5: Test CSRF token generation
echo "<h2>Test 5: CSRF Token</h2>\n";
$csrf_token = generateCSRFToken();
echo "CSRF Token generated: " . (!empty($csrf_token) ? 'YES' : 'NO') . "<br>\n";
echo "Token length: " . strlen($csrf_token) . "<br>\n";

// Test 6: Test input sanitization
echo "<h2>Test 6: Input Sanitization</h2>\n";
$test_inputs = [
    'normal text',
    '<script>alert("xss")</script>',
    'text with "quotes" and \'apostrophes\'',
    'text with & ampersands',
    ''
];

foreach ($test_inputs as $input) {
    $sanitized = sanitizeInput($input);
    echo "Original: '$input' -> Sanitized: '$sanitized'<br>\n";
}

echo "<h2>Test Complete</h2>\n";
echo "All basic functions are working correctly.<br>\n";
echo "<a href='login.php'>Go to Login Page</a><br>\n";
echo "<a href='dashboard.php'>Go to Dashboard</a><br>\n";
?>
