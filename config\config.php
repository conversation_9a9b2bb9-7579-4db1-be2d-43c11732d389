<?php
/**
 * FTP-Based Portal Application Configuration
 * No database required - all authentication via FTP
 */

// Start session with secure settings
if (session_status() == PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_only_cookies', 1);
    session_start();
}

// Application settings
define('APP_NAME', 'FTP Portal');
define('APP_VERSION', '2.0.0');
define('BASE_URL', 'http://localhost/portal-dev/');

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour

// FTP Configuration - Demo mode for development
define('FTP_HOST', 'localhost'); // Your FTP server host
define('FTP_PORT', 21); // FTP port (21 for standard FTP, 22 for SFTP)
define('FTP_BASE_PATH', '/invoices/'); // Base path where user folders are located
define('FTP_TIMEOUT', 30); // Connection timeout in seconds
define('FTP_DEMO_MODE', true); // Enable demo mode for development

// Payment Gateway Configuration (PayFast)
define('PAYFAST_MERCHANT_ID', 'your_merchant_id');
define('PAYFAST_MERCHANT_KEY', 'your_merchant_key');
define('PAYFAST_PASSPHRASE', 'your_passphrase');
define('PAYFAST_SANDBOX', true); // Set to false for production

// Available modules - Enhanced for specific users
define('AVAILABLE_MODULES', [
    'invoices' => 'Invoices',
    'freight' => 'Freight Management',
    'tariff' => 'Tariff Book',
    'accounting' => 'Accounting',
    'backoffice' => 'Official Website'
]);

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * Check if user is logged in via FTP
 */
function isLoggedIn() {
    return isset($_SESSION['ftp_username']) && isset($_SESSION['last_activity']) &&
           (time() - $_SESSION['last_activity'] < SESSION_TIMEOUT);
}

/**
 * Get current user's FTP folder path
 */
function getUserFTPPath() {
    if (!isLoggedIn()) {
        return null;
    }
    return FTP_BASE_PATH . $_SESSION['ftp_username'] . '/';
}

/**
 * Redirect to login if not authenticated
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . BASE_URL . 'login.php');
        exit();
    }
    $_SESSION['last_activity'] = time();
}

/**
 * Authenticate user via FTP (with demo mode support)
 */
function authenticateFTP($username, $password) {
    // Sanitize username to prevent path traversal
    $username = sanitizeFTPUsername($username);

    if (empty($username) || empty($password)) {
        return false;
    }

    // Demo mode for development
    if (FTP_DEMO_MODE) {
        return authenticateFTPDemo($username, $password);
    }

    // Real FTP authentication
    $connection = ftp_connect(FTP_HOST, FTP_PORT, FTP_TIMEOUT);

    if (!$connection) {
        error_log("FTP connection failed for user: " . $username);
        return false;
    }

    // Attempt login
    if (ftp_login($connection, $username, $password)) {
        // Set passive mode
        ftp_pasv($connection, true);

        // Verify user's folder exists
        $user_folder = FTP_BASE_PATH . $username . '/';
        if (ftp_chdir($connection, $user_folder)) {
            // Store session data
            $_SESSION['ftp_username'] = $username;
            $_SESSION['ftp_password'] = $password; // Store encrypted in production
            $_SESSION['user_folder'] = $user_folder;
            $_SESSION['last_activity'] = time();

            ftp_close($connection);
            return true;
        } else {
            error_log("User folder not found: " . $user_folder . " for user: " . $username);
        }
    } else {
        error_log("FTP login failed for user: " . $username);
    }

    ftp_close($connection);
    return false;
}

/**
 * Demo FTP authentication for development
 */
function authenticateFTPDemo($username, $password) {
    // Demo users with simple password validation
    $demo_users = [
        'demo' => 'password123',
        'client1' => 'password123',
        'client2' => 'password123',
        'testuser' => 'password123',
        'admin' => 'admin123'
    ];

    // Check if user exists and password matches
    if (isset($demo_users[$username]) && $demo_users[$username] === $password) {
        // Store session data
        $_SESSION['ftp_username'] = $username;
        $_SESSION['ftp_password'] = $password;
        $_SESSION['user_folder'] = FTP_BASE_PATH . $username . '/';
        $_SESSION['last_activity'] = time();

        logSecurityEvent('DEMO_LOGIN_SUCCESS', 'User: ' . $username);
        return true;
    }

    logSecurityEvent('DEMO_LOGIN_FAILED', 'User: ' . $username);
    return false;
}

/**
 * Sanitize FTP username to prevent path traversal
 */
function sanitizeFTPUsername($username) {
    // Remove any path traversal attempts
    $username = str_replace(['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '', $username);
    // Remove any whitespace
    $username = trim($username);
    // Convert to lowercase for consistency
    $username = strtolower($username);
    // Limit length
    $username = substr($username, 0, 50);

    return $username;
}

/**
 * Get FTP connection for current user (with demo mode support)
 */
function getFTPConnection() {
    if (!isLoggedIn()) {
        return false;
    }

    // Demo mode - return a mock connection identifier
    if (FTP_DEMO_MODE) {
        return 'demo_connection'; // Mock connection for demo mode
    }

    // Real FTP connection
    $connection = ftp_connect(FTP_HOST, FTP_PORT, FTP_TIMEOUT);

    if (!$connection) {
        return false;
    }

    if (ftp_login($connection, $_SESSION['ftp_username'], $_SESSION['ftp_password'])) {
        ftp_pasv($connection, true);
        return $connection;
    }

    ftp_close($connection);
    return false;
}

/**
 * Check if connection is a real FTP connection
 */
function isRealFTPConnection($connection) {
    return $connection && $connection !== 'demo_connection' && is_resource($connection);
}

/**
 * Get demo files for current user
 */
function getDemoFiles($username) {
    $demo_files = [
        'demo' => [
            ['filename' => 'invoice_2024_001.pdf', 'size' => 245760, 'modified' => time() - 86400],
            ['filename' => 'invoice_2024_002.pdf', 'size' => 189440, 'modified' => time() - 172800],
            ['filename' => 'customs_declaration_001.pdf', 'size' => 156672, 'modified' => time() - 259200]
        ],
        'client1' => [
            ['filename' => 'commercial_invoice_C1_001.pdf', 'size' => 298752, 'modified' => time() - 43200],
            ['filename' => 'commercial_invoice_C1_002.pdf', 'size' => 267264, 'modified' => time() - 129600],
            ['filename' => 'shipping_manifest_001.pdf', 'size' => 198656, 'modified' => time() - 216000]
        ],
        'client2' => [
            ['filename' => 'export_invoice_2024_001.pdf', 'size' => 234496, 'modified' => time() - 21600],
            ['filename' => 'duty_assessment_001.pdf', 'size' => 176128, 'modified' => time() - 108000],
            ['filename' => 'clearance_certificate_001.pdf', 'size' => 145408, 'modified' => time() - 194400]
        ],
        'testuser' => [
            ['filename' => 'test_invoice_001.pdf', 'size' => 123456, 'modified' => time() - 3600],
            ['filename' => 'sample_document.pdf', 'size' => 98304, 'modified' => time() - 7200]
        ],
        'admin' => [
            ['filename' => 'admin_report_001.pdf', 'size' => 345678, 'modified' => time() - 1800],
            ['filename' => 'system_overview.pdf', 'size' => 456789, 'modified' => time() - 5400],
            ['filename' => 'compliance_audit.pdf', 'size' => 234567, 'modified' => time() - 9000]
        ]
    ];

    return isset($demo_files[$username]) ? $demo_files[$username] : [];
}

/**
 * Validate file path to prevent directory traversal
 */
function validateFilePath($filename) {
    // Remove any path traversal attempts
    $filename = basename($filename);

    // Check for dangerous characters
    if (preg_match('/[^a-zA-Z0-9._-]/', $filename)) {
        return false;
    }

    // Check file extension (only allow PDF files)
    if (!preg_match('/\.pdf$/i', $filename)) {
        return false;
    }

    // Check filename length
    if (strlen($filename) > 255) {
        return false;
    }

    return $filename;
}

/**
 * Secure file download with validation (demo mode support)
 */
function secureFileDownload($filename) {
    // Validate filename
    $safe_filename = validateFilePath($filename);
    if (!$safe_filename) {
        return false;
    }

    // Demo mode - generate demo PDF
    if (FTP_DEMO_MODE) {
        return downloadDemoFile($safe_filename);
    }

    // Get user's FTP folder
    $user_folder = getUserFTPPath();
    if (!$user_folder) {
        return false;
    }

    // Connect to FTP
    $connection = getFTPConnection();
    if (!$connection) {
        return false;
    }

    // Verify file exists in user's folder
    $remote_file = $user_folder . $safe_filename;
    $file_size = ftp_size($connection, $remote_file);

    if ($file_size === -1) {
        ftp_close($connection);
        return false;
    }

    // Download file to temporary location
    $temp_file = tempnam(sys_get_temp_dir(), 'secure_download_');

    if (ftp_get($connection, $temp_file, $remote_file, FTP_BINARY)) {
        ftp_close($connection);

        // Set secure headers
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $safe_filename . '"');
        header('Content-Length: ' . filesize($temp_file));
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Output file and clean up
        readfile($temp_file);
        unlink($temp_file);
        return true;
    }

    ftp_close($connection);
    unlink($temp_file);
    return false;
}

/**
 * Download demo file (generates PDF on the fly)
 */
function downloadDemoFile($filename) {
    // Verify file exists in demo data
    $demo_files = getDemoFiles($_SESSION['ftp_username']);
    $file_exists = false;

    foreach ($demo_files as $file) {
        if ($file['filename'] === $filename) {
            $file_exists = true;
            break;
        }
    }

    if (!$file_exists) {
        return false;
    }

    // Generate demo PDF content
    $pdf_content = generateDemoPDF($filename, $_SESSION['ftp_username']);

    // Set secure headers
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . strlen($pdf_content));
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');

    // Output PDF
    echo $pdf_content;
    return true;
}

/**
 * Generate demo PDF content
 */
function generateDemoPDF($filename, $username) {
    $amount = number_format(100 + (crc32($filename) % 500), 2);
    $date = date('Y-m-d');
    $invoice_number = 'INV-' . strtoupper(substr($username, 0, 3)) . '-' . date('Y') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);

    $pdf_content = "%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj
4 0 obj
<<
/Length 400
>>
stream
BT
/F1 16 Tf
50 750 Td
(COMMERCIAL INVOICE - DEMO) Tj
0 -40 Td
/F1 12 Tf
(Invoice Number: $invoice_number) Tj
0 -20 Td
(Filename: $filename) Tj
0 -20 Td
(Client: $username) Tj
0 -20 Td
(Date: $date) Tj
0 -20 Td
(Amount: \$$amount USD) Tj
0 -30 Td
(Status: PENDING PAYMENT) Tj
0 -40 Td
(This is a demo invoice generated by the FTP Portal system.) Tj
0 -20 Td
(In production, this would be a real PDF from your FTP server.) Tj
0 -40 Td
(Demo Mode: Enabled) Tj
ET
endstream
endobj
5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
xref
0 6
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000273 00000 n
0000000725 00000 n
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
822
%%EOF";

    return $pdf_content;
}

/**
 * Check demo payment status
 */
function checkDemoPaymentStatus($invoice_name, $username) {
    // In demo mode, use session storage to track payments
    if (!isset($_SESSION['demo_payments'])) {
        $_SESSION['demo_payments'] = [];
    }

    $payment_key = $username . '_' . $invoice_name;
    return isset($_SESSION['demo_payments'][$payment_key]);
}

/**
 * Create demo payment success file
 */
function createDemoPaymentFile($invoice_name, $payment_reference, $amount, $username) {
    // In demo mode, store payment in session
    if (!isset($_SESSION['demo_payments'])) {
        $_SESSION['demo_payments'] = [];
    }

    $payment_key = $username . '_' . $invoice_name;
    $_SESSION['demo_payments'][$payment_key] = [
        'invoice_name' => $invoice_name,
        'payment_reference' => $payment_reference,
        'amount' => $amount,
        'payment_date' => date('Y-m-d H:i:s'),
        'user' => $username,
        'status' => 'PAID'
    ];

    logSecurityEvent('DEMO_PAYMENT_SUCCESS', 'Invoice: ' . $invoice_name . ', Reference: ' . $payment_reference);
    return true;
}

/**
 * Log security events
 */
function logSecurityEvent($event, $details = '') {
    $log_entry = date('Y-m-d H:i:s') . ' - ' . $event;
    if ($details) {
        $log_entry .= ' - ' . $details;
    }
    $log_entry .= ' - IP: ' . $_SERVER['REMOTE_ADDR'];
    if (isLoggedIn()) {
        $log_entry .= ' - User: ' . $_SESSION['ftp_username'];
    }
    error_log($log_entry);
}
?>
