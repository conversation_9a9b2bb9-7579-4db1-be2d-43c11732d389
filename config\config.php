<?php
/**
 * FTP-Based Portal Application Configuration
 * No database required - all authentication via FTP
 */

// Start session with secure settings
if (session_status() == PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_only_cookies', 1);
    session_start();
}

// Application settings
define('APP_NAME', 'FTP Portal');
define('APP_VERSION', '2.0.0');
define('BASE_URL', 'http://localhost/portal-dev/');

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour

// FTP Configuration - Users will authenticate with their own FTP credentials
define('FTP_HOST', 'localhost'); // Your FTP server host
define('FTP_PORT', 21); // FTP port (21 for standard FTP, 22 for SFTP)
define('FTP_BASE_PATH', '/invoices/'); // Base path where user folders are located
define('FTP_TIMEOUT', 30); // Connection timeout in seconds

// Payment Gateway Configuration (PayFast)
define('PAYFAST_MERCHANT_ID', 'your_merchant_id');
define('PAYFAST_MERCHANT_KEY', 'your_merchant_key');
define('PAYFAST_PASSPHRASE', 'your_passphrase');
define('PAYFAST_SANDBOX', true); // Set to false for production

// Available modules - Enhanced for specific users
define('AVAILABLE_MODULES', [
    'invoices' => 'Invoices',
    'freight' => 'Freight Management',
    'tariff' => 'Tariff Book',
    'accounting' => 'Accounting',
    'backoffice' => 'Official Website'
]);

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * Check if user is logged in via FTP
 */
function isLoggedIn() {
    return isset($_SESSION['ftp_username']) && isset($_SESSION['last_activity']) &&
           (time() - $_SESSION['last_activity'] < SESSION_TIMEOUT);
}

/**
 * Get current user's FTP folder path
 */
function getUserFTPPath() {
    if (!isLoggedIn()) {
        return null;
    }
    return FTP_BASE_PATH . $_SESSION['ftp_username'] . '/';
}

/**
 * Redirect to login if not authenticated
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . BASE_URL . 'login.php');
        exit();
    }
    $_SESSION['last_activity'] = time();
}

/**
 * Authenticate user via FTP
 */
function authenticateFTP($username, $password) {
    // Sanitize username to prevent path traversal
    $username = sanitizeFTPUsername($username);

    if (empty($username) || empty($password)) {
        return false;
    }

    // Attempt FTP connection
    $connection = ftp_connect(FTP_HOST, FTP_PORT, FTP_TIMEOUT);

    if (!$connection) {
        error_log("FTP connection failed for user: " . $username);
        return false;
    }

    // Attempt login
    if (ftp_login($connection, $username, $password)) {
        // Set passive mode
        ftp_pasv($connection, true);

        // Verify user's folder exists
        $user_folder = FTP_BASE_PATH . $username . '/';
        if (ftp_chdir($connection, $user_folder)) {
            // Store session data
            $_SESSION['ftp_username'] = $username;
            $_SESSION['ftp_password'] = $password; // Store encrypted in production
            $_SESSION['user_folder'] = $user_folder;
            $_SESSION['last_activity'] = time();

            ftp_close($connection);
            return true;
        } else {
            error_log("User folder not found: " . $user_folder . " for user: " . $username);
        }
    } else {
        error_log("FTP login failed for user: " . $username);
    }

    ftp_close($connection);
    return false;
}

/**
 * Sanitize FTP username to prevent path traversal
 */
function sanitizeFTPUsername($username) {
    // Remove any path traversal attempts
    $username = str_replace(['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '', $username);
    // Remove any whitespace
    $username = trim($username);
    // Convert to lowercase for consistency
    $username = strtolower($username);
    // Limit length
    $username = substr($username, 0, 50);

    return $username;
}

/**
 * Get FTP connection for current user
 */
function getFTPConnection() {
    if (!isLoggedIn()) {
        return false;
    }

    $connection = ftp_connect(FTP_HOST, FTP_PORT, FTP_TIMEOUT);

    if (!$connection) {
        return false;
    }

    if (ftp_login($connection, $_SESSION['ftp_username'], $_SESSION['ftp_password'])) {
        ftp_pasv($connection, true);
        return $connection;
    }

    ftp_close($connection);
    return false;
}

/**
 * Validate file path to prevent directory traversal
 */
function validateFilePath($filename) {
    // Remove any path traversal attempts
    $filename = basename($filename);

    // Check for dangerous characters
    if (preg_match('/[^a-zA-Z0-9._-]/', $filename)) {
        return false;
    }

    // Check file extension (only allow PDF files)
    if (!preg_match('/\.pdf$/i', $filename)) {
        return false;
    }

    // Check filename length
    if (strlen($filename) > 255) {
        return false;
    }

    return $filename;
}

/**
 * Secure file download with validation
 */
function secureFileDownload($filename) {
    // Validate filename
    $safe_filename = validateFilePath($filename);
    if (!$safe_filename) {
        return false;
    }

    // Get user's FTP folder
    $user_folder = getUserFTPPath();
    if (!$user_folder) {
        return false;
    }

    // Connect to FTP
    $connection = getFTPConnection();
    if (!$connection) {
        return false;
    }

    // Verify file exists in user's folder
    $remote_file = $user_folder . $safe_filename;
    $file_size = ftp_size($connection, $remote_file);

    if ($file_size === -1) {
        ftp_close($connection);
        return false;
    }

    // Download file to temporary location
    $temp_file = tempnam(sys_get_temp_dir(), 'secure_download_');

    if (ftp_get($connection, $temp_file, $remote_file, FTP_BINARY)) {
        ftp_close($connection);

        // Set secure headers
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $safe_filename . '"');
        header('Content-Length: ' . filesize($temp_file));
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Output file and clean up
        readfile($temp_file);
        unlink($temp_file);
        return true;
    }

    ftp_close($connection);
    unlink($temp_file);
    return false;
}

/**
 * Log security events
 */
function logSecurityEvent($event, $details = '') {
    $log_entry = date('Y-m-d H:i:s') . ' - ' . $event;
    if ($details) {
        $log_entry .= ' - ' . $details;
    }
    $log_entry .= ' - IP: ' . $_SERVER['REMOTE_ADDR'];
    if (isLoggedIn()) {
        $log_entry .= ' - User: ' . $_SESSION['ftp_username'];
    }
    error_log($log_entry);
}
?>
