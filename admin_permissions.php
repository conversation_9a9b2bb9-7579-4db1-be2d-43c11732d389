<?php
/**
 * Admin Module Permissions Management
 * Interface for managing user access to different modules
 */

require_once 'config/config.php';

// Require admin access
requireLogin();
if (!isAdmin()) {
    header('Location: dashboard.php');
    exit();
}

// Initialize permissions
initializeUserPermissions();

$success_message = '';
$error_message = '';

// Handle permission updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid request. Please try again.';
    } else {
        if (isset($_POST['bulk_update'])) {
            // Handle bulk update
            $user_permissions = [];
            foreach ($_POST as $key => $value) {
                if (strpos($key, 'user_') === 0) {
                    $parts = explode('_', $key, 3);
                    if (count($parts) === 3) {
                        $username = $parts[1];
                        $module = $parts[2];
                        if (!isset($user_permissions[$username])) {
                            $user_permissions[$username] = [];
                        }
                        $user_permissions[$username][] = $module;
                    }
                }
            }
            
            if (bulkUpdatePermissions($user_permissions)) {
                $success_message = 'Permissions updated successfully for all users.';
            } else {
                $error_message = 'Failed to update permissions.';
            }
        } elseif (isset($_POST['single_update'])) {
            // Handle single user update
            $username = sanitizeInput($_POST['username']);
            $modules = isset($_POST['modules']) ? $_POST['modules'] : [];
            
            if (updateUserPermissions($username, $modules)) {
                $success_message = "Permissions updated successfully for user: $username";
            } else {
                $error_message = 'Failed to update user permissions.';
            }
        }
    }
}

// Get current data
$all_users = getAllDemoUsers();
$permissions_matrix = getPermissionsMatrix();
$selected_user = isset($_GET['user']) ? sanitizeInput($_GET['user']) : '';

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Module Permissions - Admin Portal</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .breadcrumb {
            color: rgba(255,255,255,0.8);
            margin-bottom: 10px;
        }
        .breadcrumb a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
        }
        .breadcrumb a:hover {
            color: white;
        }
        .admin-section {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f3f4f6;
        }
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #1e3a8a;
        }
        .permissions-grid {
            display: grid;
            grid-template-columns: 200px 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .user-list {
            background: #f9fafb;
            border-radius: 8px;
            padding: 15px;
        }
        .user-item {
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .user-item:hover {
            background: #e5e7eb;
        }
        .user-item.active {
            background: #3b82f6;
            color: white;
        }
        .permissions-panel {
            background: #f9fafb;
            border-radius: 8px;
            padding: 20px;
        }
        .module-permission {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .module-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .module-icon {
            font-size: 1.5em;
        }
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 25px;
            background: #d1d5db;
            border-radius: 25px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .toggle-switch.active {
            background: #22c55e;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        .toggle-switch.active::after {
            transform: translateX(25px);
        }
        .btn-admin {
            background: #1e3a8a;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9em;
            cursor: pointer;
        }
        .btn-admin:hover {
            background: #1e40af;
        }
        .btn-success {
            background: #22c55e;
        }
        .btn-success:hover {
            background: #16a34a;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .bulk-actions {
            background: #f0f9ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3b82f6;
        }
        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
    </style>
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-logo">
                        <img src="assets/img/logo.png" alt="<?php echo APP_NAME; ?>" class="logo-image">
                    </div>
                    <div class="brand-text">
                        <span class="brand-subtitle">Admin Portal</span>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-menu-container">
                    <button class="user-menu-trigger" onclick="toggleSettings()">
                        <div class="user-avatar-small">
                            <span><?php echo strtoupper(substr($_SESSION['ftp_username'], 0, 2)); ?></span>
                        </div>
                        <div class="user-info-compact">
                            <div class="user-name-compact"><?php echo htmlspecialchars($_SESSION['ftp_username']); ?></div>
                            <div class="user-role-compact">Administrator</div>
                        </div>
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor" class="dropdown-chevron">
                            <path d="M2 4l4 4 4-4H2z"/>
                        </svg>
                    </button>
                    
                    <div class="user-menu-dropdown" id="settingsDropdown">
                        <div class="user-menu-header">
                            <div class="user-profile-info">
                                <div class="user-name-full"><?php echo htmlspecialchars($_SESSION['ftp_username']); ?></div>
                                <div class="user-details-full">
                                    <span class="client-name">System Administrator</span>
                                    <span class="role-badge-small">Admin</span>
                                </div>
                            </div>
                        </div>
                        <div class="menu-divider"></div>
                        <a href="admin_dashboard.php" class="user-menu-item">
                            Admin Dashboard
                        </a>
                        <a href="dashboard.php" class="user-menu-item">
                            User Dashboard
                        </a>
                        <div class="menu-divider"></div>
                        <a href="logout.php" class="user-menu-item logout-item">
                            Sign Out
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="admin-header">
            <div class="container">
                <div class="breadcrumb">
                    <a href="admin_dashboard.php">Admin Dashboard</a> / Module Permissions
                </div>
                <h1>🔐 Module Permissions Management</h1>
                <p>Control user access to different portal modules</p>
            </div>
        </div>

        <div class="container">
            <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
            <?php endif; ?>

            <!-- Bulk Permissions Management -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">⚡ Bulk Permissions Management</h2>
                </div>

                <form method="POST" class="bulk-actions">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="bulk_update" value="1">

                    <h3 style="margin-top: 0;">Quick Assign Modules to Multiple Users</h3>
                    <p style="color: #6b7280;">Select modules and users to quickly assign permissions.</p>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px;">
                        <div>
                            <h4>Select Modules:</h4>
                            <div class="checkbox-grid">
                                <?php foreach (AVAILABLE_MODULES as $module_key => $module_info): ?>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="bulk_module_<?php echo $module_key; ?>"
                                           name="bulk_modules[]" value="<?php echo $module_key; ?>"
                                           onchange="updateBulkPermissions()">
                                    <label for="bulk_module_<?php echo $module_key; ?>">
                                        <?php echo $module_info['icon']; ?> <?php echo $module_info['name']; ?>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div>
                            <h4>Select Users:</h4>
                            <div class="checkbox-grid">
                                <?php foreach ($all_users as $username => $user_data): ?>
                                    <?php if ($username !== 'admin'): ?>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="bulk_user_<?php echo $username; ?>"
                                               name="bulk_users[]" value="<?php echo $username; ?>"
                                               onchange="updateBulkPermissions()">
                                        <label for="bulk_user_<?php echo $username; ?>">
                                            <?php echo htmlspecialchars($username); ?>
                                        </label>
                                    </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden inputs for bulk permissions -->
                    <div id="bulk_permissions_inputs"></div>

                    <div style="margin-top: 20px;">
                        <button type="submit" class="btn-admin btn-success">Apply Bulk Permissions</button>
                        <button type="button" onclick="clearBulkSelections()" class="btn-admin" style="background: #6b7280;">Clear Selections</button>
                    </div>
                </form>
            </div>

            <!-- Individual User Permissions -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">👤 Individual User Permissions</h2>
                </div>

                <div class="permissions-grid">
                    <div class="user-list">
                        <h4 style="margin-top: 0;">Select User:</h4>
                        <?php foreach ($all_users as $username => $user_data): ?>
                            <?php if ($username !== 'admin'): ?>
                            <div class="user-item <?php echo $selected_user === $username ? 'active' : ''; ?>"
                                 onclick="selectUser('<?php echo $username; ?>')">
                                <strong><?php echo htmlspecialchars($username); ?></strong><br>
                                <small style="opacity: 0.8;">
                                    <?php
                                    $user_perms = isset($permissions_matrix[$username]) ? $permissions_matrix[$username] : [];
                                    echo array_sum($user_perms); ?> of <?php echo count(AVAILABLE_MODULES); ?> modules
                                </small>
                            </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>

                    <div class="permissions-panel">
                        <?php if ($selected_user && isset($permissions_matrix[$selected_user])): ?>
                        <form method="POST" id="user_permissions_form">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            <input type="hidden" name="single_update" value="1">
                            <input type="hidden" name="username" value="<?php echo $selected_user; ?>">

                            <h4 style="margin-top: 0;">Module Access for: <?php echo htmlspecialchars($selected_user); ?></h4>

                            <?php foreach (AVAILABLE_MODULES as $module_key => $module_info): ?>
                            <div class="module-permission">
                                <div class="module-info">
                                    <div class="module-icon"><?php echo $module_info['icon']; ?></div>
                                    <div>
                                        <strong><?php echo htmlspecialchars($module_info['name']); ?></strong><br>
                                        <small style="color: #6b7280;"><?php echo htmlspecialchars($module_info['description']); ?></small>
                                    </div>
                                </div>
                                <div>
                                    <input type="checkbox" name="modules[]" value="<?php echo $module_key; ?>"
                                           id="module_<?php echo $module_key; ?>"
                                           <?php echo $permissions_matrix[$selected_user][$module_key] ? 'checked' : ''; ?>
                                           style="display: none;">
                                    <div class="toggle-switch <?php echo $permissions_matrix[$selected_user][$module_key] ? 'active' : ''; ?>"
                                         onclick="toggleModule('<?php echo $module_key; ?>')"></div>
                                </div>
                            </div>
                            <?php endforeach; ?>

                            <div style="margin-top: 20px;">
                                <button type="submit" class="btn-admin btn-success">Save Permissions</button>
                                <a href="admin_permissions.php" class="btn-admin" style="background: #6b7280;">Cancel</a>
                            </div>
                        </form>
                        <?php else: ?>
                        <div style="text-align: center; color: #6b7280; padding: 50px;">
                            <h4>Select a user from the list to manage their module permissions</h4>
                            <p>Click on any user name to view and edit their access rights.</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function toggleSettings() {
            const dropdown = document.getElementById('settingsDropdown');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        function selectUser(username) {
            window.location.href = 'admin_permissions.php?user=' + encodeURIComponent(username);
        }

        function toggleModule(moduleKey) {
            const checkbox = document.getElementById('module_' + moduleKey);
            const toggle = checkbox.nextElementSibling;

            checkbox.checked = !checkbox.checked;
            toggle.classList.toggle('active');
        }

        function updateBulkPermissions() {
            const selectedModules = Array.from(document.querySelectorAll('input[name="bulk_modules[]"]:checked')).map(cb => cb.value);
            const selectedUsers = Array.from(document.querySelectorAll('input[name="bulk_users[]"]:checked')).map(cb => cb.value);

            const container = document.getElementById('bulk_permissions_inputs');
            container.innerHTML = '';

            selectedUsers.forEach(user => {
                selectedModules.forEach(module => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'user_' + user + '_' + module;
                    input.value = '1';
                    container.appendChild(input);
                });
            });
        }

        function clearBulkSelections() {
            document.querySelectorAll('input[name="bulk_modules[]"]').forEach(cb => cb.checked = false);
            document.querySelectorAll('input[name="bulk_users[]"]').forEach(cb => cb.checked = false);
            document.getElementById('bulk_permissions_inputs').innerHTML = '';
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('settingsDropdown');
            const trigger = document.querySelector('.user-menu-trigger');

            if (!trigger.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
    </script>
</body>
</html>
