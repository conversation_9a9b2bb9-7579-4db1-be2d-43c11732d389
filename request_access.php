<?php
/**
 * Request Module Access Page
 * Allows users to request access to additional modules
 */

require_once 'config/config.php';

// Require login
requireLogin();

// Redirect admin users
if (isAdmin()) {
    header('Location: admin_dashboard.php');
    exit();
}

$success_message = '';
$error_message = '';

// Handle access request submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid request. Please try again.';
    } else {
        $requested_modules = isset($_POST['modules']) ? $_POST['modules'] : [];
        $message = sanitizeInput($_POST['message']);
        
        if (empty($requested_modules)) {
            $error_message = 'Please select at least one module to request access to.';
        } else {
            // In a real system, this would send an email or create a ticket
            // For demo purposes, we'll just log the request
            $request_details = [
                'user' => $_SESSION['ftp_username'],
                'modules' => $requested_modules,
                'message' => $message,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            // Store request in session for demo
            if (!isset($_SESSION['access_requests'])) {
                $_SESSION['access_requests'] = [];
            }
            $_SESSION['access_requests'][] = $request_details;
            
            logSecurityEvent('ACCESS_REQUEST', 'User: ' . $_SESSION['ftp_username'] . ', Modules: ' . implode(',', $requested_modules));
            $success_message = 'Your access request has been submitted successfully. An administrator will review your request.';
        }
    }
}

// Get user's current permissions and available modules
$accessible_modules = getAccessibleModules();
$all_modules = AVAILABLE_MODULES;
$unavailable_modules = [];

foreach ($all_modules as $module_key => $module_info) {
    if (!isset($accessible_modules[$module_key])) {
        $unavailable_modules[$module_key] = $module_info;
    }
}

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Module Access - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .request-header {
            background: linear-gradient(135deg, #2B5E5F 0%, #3b82f6 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .breadcrumb {
            color: rgba(255,255,255,0.8);
            margin-bottom: 10px;
        }
        .breadcrumb a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
        }
        .breadcrumb a:hover {
            color: white;
        }
        .request-section {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f3f4f6;
        }
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2B5E5F;
        }
        .module-card {
            background: #f9fafb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #3b82f6;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .module-icon {
            font-size: 2em;
        }
        .module-info {
            flex: 1;
        }
        .module-checkbox {
            margin-left: auto;
        }
        .btn {
            background: #2B5E5F;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9em;
            cursor: pointer;
        }
        .btn:hover {
            background: #1e4344;
        }
        .btn-secondary {
            background: #6b7280;
        }
        .btn-secondary:hover {
            background: #4b5563;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #374151;
        }
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 5px;
            font-family: inherit;
            resize: vertical;
        }
        .current-access {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .access-badge {
            display: inline-block;
            background: #22c55e;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8em;
            margin: 2px;
        }
    </style>
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-logo">
                        <img src="assets/img/logo.png" alt="<?php echo APP_NAME; ?>" class="logo-image">
                    </div>
                    <div class="brand-text">
                        <span class="brand-subtitle">Portal</span>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-menu-container">
                    <button class="user-menu-trigger" onclick="toggleSettings()">
                        <div class="user-avatar-small">
                            <span><?php echo strtoupper(substr($_SESSION['ftp_username'], 0, 2)); ?></span>
                        </div>
                        <div class="user-info-compact">
                            <div class="user-name-compact"><?php echo htmlspecialchars($_SESSION['ftp_username']); ?></div>
                            <div class="user-role-compact">FTP User</div>
                        </div>
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor" class="dropdown-chevron">
                            <path d="M2 4l4 4 4-4H2z"/>
                        </svg>
                    </button>
                    
                    <div class="user-menu-dropdown" id="settingsDropdown">
                        <div class="user-menu-header">
                            <div class="user-profile-info">
                                <div class="user-name-full"><?php echo htmlspecialchars($_SESSION['ftp_username']); ?></div>
                                <div class="user-details-full">
                                    <span class="client-name">FTP Folder: <?php echo htmlspecialchars($_SESSION['user_folder']); ?></span>
                                    <span class="role-badge-small">FTP User</span>
                                </div>
                            </div>
                        </div>
                        <div class="menu-divider"></div>
                        <a href="dashboard.php" class="user-menu-item">
                            Dashboard
                        </a>
                        <a href="demo_status.php" class="user-menu-item">
                            Demo Status
                        </a>
                        <div class="menu-divider"></div>
                        <a href="logout.php" class="user-menu-item logout-item">
                            Sign Out
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="request-header">
            <div class="container">
                <div class="breadcrumb">
                    <a href="dashboard.php">Dashboard</a> / Request Module Access
                </div>
                <h1>🔑 Request Module Access</h1>
                <p>Request access to additional portal modules</p>
            </div>
        </div>

        <div class="container">
            <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
            <?php endif; ?>

            <!-- Current Access -->
            <div class="request-section">
                <div class="section-header">
                    <h2 class="section-title">✅ Your Current Access</h2>
                </div>
                
                <div class="current-access">
                    <p><strong>You currently have access to the following modules:</strong></p>
                    <?php if (!empty($accessible_modules)): ?>
                        <?php foreach ($accessible_modules as $module_key => $module_info): ?>
                            <span class="access-badge">
                                <?php echo $module_info['icon']; ?> <?php echo $module_info['name']; ?>
                            </span>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p style="color: #6b7280;">No modules currently accessible. Please request access below.</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Request Additional Access -->
            <?php if (!empty($unavailable_modules)): ?>
            <div class="request-section">
                <div class="section-header">
                    <h2 class="section-title">📝 Request Additional Access</h2>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    
                    <p>Select the modules you would like to request access to:</p>
                    
                    <?php foreach ($unavailable_modules as $module_key => $module_info): ?>
                    <div class="module-card">
                        <div class="module-icon"><?php echo $module_info['icon']; ?></div>
                        <div class="module-info">
                            <h4 style="margin: 0 0 5px 0;"><?php echo htmlspecialchars($module_info['name']); ?></h4>
                            <p style="margin: 0; color: #6b7280;"><?php echo htmlspecialchars($module_info['description']); ?></p>
                        </div>
                        <div class="module-checkbox">
                            <input type="checkbox" name="modules[]" value="<?php echo $module_key; ?>" 
                                   id="module_<?php echo $module_key; ?>">
                            <label for="module_<?php echo $module_key; ?>" style="margin-left: 5px;">Request Access</label>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    
                    <div class="form-group">
                        <label for="message">Additional Message (Optional):</label>
                        <textarea name="message" id="message" rows="4" 
                                  placeholder="Please explain why you need access to these modules..."></textarea>
                    </div>
                    
                    <div style="display: flex; gap: 15px;">
                        <button type="submit" class="btn">Submit Request</button>
                        <a href="dashboard.php" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
            <?php else: ?>
            <div class="request-section">
                <div class="alert alert-info">
                    <strong>Great!</strong> You already have access to all available modules. No additional access requests are needed.
                </div>
                <a href="dashboard.php" class="btn">Return to Dashboard</a>
            </div>
            <?php endif; ?>
        </div>
    </main>

    <script>
        function toggleSettings() {
            const dropdown = document.getElementById('settingsDropdown');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('settingsDropdown');
            const trigger = document.querySelector('.user-menu-trigger');
            
            if (!trigger.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
    </script>
</body>
</html>
