<?php
/**
 * Simple Admin Dashboard
 * Basic administrative interface with minimal styling
 */

require_once 'config/config.php';

// Require admin access
requireLogin();
if (!isAdmin()) {
    header('Location: dashboard.php');
    exit();
}

// Initialize permissions and get basic data
initializeUserPermissions();
$all_users = getAllDemoUsers();
$permissions_matrix = getPermissionsMatrix();
$system_stats = getSystemPaymentStats();

// Handle permission updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_permissions'])) {
    if (verifyCSRFToken($_POST['csrf_token'])) {
        $username = sanitizeInput($_POST['username']);
        $modules = isset($_POST['modules']) ? $_POST['modules'] : [];
        
        if (updateUserPermissions($username, $modules)) {
            $success_message = "Permissions updated for user: $username";
        } else {
            $error_message = 'Failed to update permissions.';
        }
        
        // Refresh data
        $permissions_matrix = getPermissionsMatrix();
    }
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .simple-admin {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .admin-header {
            background: #2B5E5F;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .admin-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-row {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-box {
            flex: 1;
            background: #f9fafb;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border-left: 4px solid #2B5E5F;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2B5E5F;
        }
        .permissions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .permissions-table th,
        .permissions-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .permissions-table th {
            background: #f9fafb;
            font-weight: bold;
        }
        .module-checkbox {
            margin: 5px;
        }
        .btn {
            background: #2B5E5F;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #1e4344;
        }
        .btn-secondary {
            background: #6b7280;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .user-form {
            background: #f9fafb;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .form-row {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="simple-admin">
        <div class="admin-header">
            <h1>🛡️ Admin Dashboard</h1>
            <p>Simple administration interface for <?php echo APP_NAME; ?></p>
            <div style="margin-top: 15px;">
                <a href="dashboard.php" class="btn btn-secondary">Switch to User View</a>
                <a href="logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
        <div class="alert alert-error">
            <?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>

        <!-- System Statistics -->
        <div class="admin-section">
            <h2>📊 System Overview</h2>
            <div class="stats-row">
                <div class="stat-box">
                    <div class="stat-number"><?php echo count($all_users); ?></div>
                    <div>Total Users</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">$<?php echo number_format($system_stats['total_amount'], 2); ?></div>
                    <div>Total Payments</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo $system_stats['pending_payments']; ?></div>
                    <div>Pending Payments</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo FTP_DEMO_MODE ? 'DEMO' : 'LIVE'; ?></div>
                    <div>System Mode</div>
                </div>
            </div>
        </div>

        <!-- User Permissions Management -->
        <div class="admin-section">
            <h2>👥 User Permissions</h2>
            <p>Manage which modules each user can access:</p>
            
            <table class="permissions-table">
                <thead>
                    <tr>
                        <th>User</th>
                        <?php foreach (AVAILABLE_MODULES as $module_key => $module_info): ?>
                        <th style="text-align: center;">
                            <?php echo $module_info['icon']; ?><br>
                            <small><?php echo $module_info['name']; ?></small>
                        </th>
                        <?php endforeach; ?>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($permissions_matrix as $username => $user_permissions): ?>
                    <tr>
                        <td><strong><?php echo htmlspecialchars($username); ?></strong></td>
                        <?php foreach (AVAILABLE_MODULES as $module_key => $module_info): ?>
                        <td style="text-align: center;">
                            <?php if ($user_permissions[$module_key]): ?>
                                <span style="color: #22c55e; font-size: 1.2em;">✓</span>
                            <?php else: ?>
                                <span style="color: #ef4444; font-size: 1.2em;">✗</span>
                            <?php endif; ?>
                        </td>
                        <?php endforeach; ?>
                        <td>
                            <button onclick="editUser('<?php echo $username; ?>')" class="btn" style="font-size: 0.8em; padding: 5px 10px;">
                                Edit
                            </button>
                        </td>
                    </tr>
                    
                    <!-- Edit form (hidden by default) -->
                    <tr id="edit_<?php echo $username; ?>" style="display: none;">
                        <td colspan="<?php echo count(AVAILABLE_MODULES) + 2; ?>">
                            <form method="POST" class="user-form">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <input type="hidden" name="update_permissions" value="1">
                                <input type="hidden" name="username" value="<?php echo $username; ?>">
                                
                                <div class="form-row">
                                    <strong>Edit permissions for: <?php echo htmlspecialchars($username); ?></strong>
                                </div>
                                
                                <div class="form-row">
                                    <?php foreach (AVAILABLE_MODULES as $module_key => $module_info): ?>
                                    <label class="module-checkbox">
                                        <input type="checkbox" name="modules[]" value="<?php echo $module_key; ?>" 
                                               <?php echo $user_permissions[$module_key] ? 'checked' : ''; ?>>
                                        <?php echo $module_info['icon']; ?> <?php echo $module_info['name']; ?>
                                    </label>
                                    <?php endforeach; ?>
                                </div>
                                
                                <div class="form-row">
                                    <button type="submit" class="btn">Save Changes</button>
                                    <button type="button" onclick="cancelEdit('<?php echo $username; ?>')" class="btn btn-secondary">Cancel</button>
                                </div>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Quick Links -->
        <div class="admin-section">
            <h2>🔗 Quick Links</h2>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="admin_dashboard.php" class="btn">Full Admin Dashboard</a>
                <a href="admin_user_management.php" class="btn">User Management</a>
                <a href="admin_permissions.php" class="btn">Advanced Permissions</a>
                <a href="demo_status.php" class="btn btn-secondary">Demo Status</a>
                <a href="test_demo_system.php" class="btn btn-secondary">Run Tests</a>
            </div>
        </div>
    </div>

    <script>
        function editUser(username) {
            // Hide all edit forms
            const allForms = document.querySelectorAll('[id^="edit_"]');
            allForms.forEach(form => form.style.display = 'none');
            
            // Show the selected user's form
            document.getElementById('edit_' + username).style.display = 'table-row';
        }

        function cancelEdit(username) {
            document.getElementById('edit_' + username).style.display = 'none';
        }
    </script>
</body>
</html>
