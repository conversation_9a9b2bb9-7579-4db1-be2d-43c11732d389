<?php
/**
 * Official Website Redirect
 * Redirects to the main company website
 */

require_once '../config/config.php';

// Require login
requireLogin();

// Redirect to official website
header('Location: https://your-company-website.com');
exit();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Back Office - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <h1><?php echo APP_NAME; ?> - Back Office</h1>
            <div class="user-info">
                <a href="../dashboard.php" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                <a href="../logout.php" class="btn btn-sm btn-danger">Logout</a>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="module-header">
            <h2>🏢 Back Office</h2>
            <p>Administrative functions and system management</p>
        </div>
        
        <div class="placeholder-content">
            <div class="placeholder-card">
                <div class="placeholder-icon">🏢</div>
                <h3>Back Office Module</h3>
                <p>This module is under development and will include:</p>
                <ul>
                    <li>Document management</li>
                    <li>Workflow automation</li>
                    <li>System configuration</li>
                    <li>Data import/export tools</li>
                    <li>Audit trails and logging</li>
                    <li>System monitoring and alerts</li>
                </ul>
                <p><strong>Coming Soon!</strong></p>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/script.js"></script>
</body>
</html>

<style>
.placeholder-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.placeholder-card {
    background: white;
    padding: 3rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    max-width: 500px;
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.placeholder-card h3 {
    color: #333;
    margin-bottom: 1rem;
}

.placeholder-card p {
    color: #666;
    margin-bottom: 1rem;
}

.placeholder-card ul {
    text-align: left;
    color: #666;
    margin: 1rem 0;
}

.placeholder-card li {
    margin-bottom: 0.5rem;
}
</style>
