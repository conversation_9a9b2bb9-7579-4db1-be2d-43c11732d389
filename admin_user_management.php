<?php
/**
 * Admin User Management Page
 * Comprehensive user permission management interface
 */

require_once 'config/config.php';

// Require admin access
requireLogin();
if (!isAdmin()) {
    header('Location: dashboard.php');
    exit();
}

// Initialize permissions
initializeUserPermissions();

$success_message = '';
$error_message = '';

// Handle permission updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid request. Please try again.';
    } else {
        if (isset($_POST['bulk_update'])) {
            // Handle bulk update
            $user_permissions = [];
            foreach ($_POST as $key => $value) {
                if (strpos($key, 'user_') === 0) {
                    $parts = explode('_', $key, 3);
                    if (count($parts) === 3) {
                        $username = $parts[1];
                        $module = $parts[2];
                        if (!isset($user_permissions[$username])) {
                            $user_permissions[$username] = [];
                        }
                        $user_permissions[$username][] = $module;
                    }
                }
            }
            
            if (bulkUpdatePermissions($user_permissions)) {
                $success_message = 'Permissions updated successfully for all users.';
            } else {
                $error_message = 'Failed to update permissions.';
            }
        } elseif (isset($_POST['single_update'])) {
            // Handle single user update
            $username = sanitizeInput($_POST['username']);
            $modules = isset($_POST['modules']) ? $_POST['modules'] : [];
            
            if (updateUserPermissions($username, $modules)) {
                $success_message = "Permissions updated successfully for user: $username";
            } else {
                $error_message = 'Failed to update user permissions.';
            }
        }
    }
}

// Get current data
$all_users = getAllDemoUsers();
$permissions_matrix = getPermissionsMatrix();
$module_usage_stats = getModuleUsageStats();

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Admin Portal</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #2B5E5F 0%, #3b82f6 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .breadcrumb {
            color: rgba(255,255,255,0.8);
            margin-bottom: 10px;
        }
        .breadcrumb a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
        }
        .breadcrumb a:hover {
            color: white;
        }
        .admin-section {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f3f4f6;
        }
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2B5E5F;
        }
        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .user-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        .user-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .user-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #2B5E5F 0%, #3b82f6 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2em;
        }
        .user-info h3 {
            margin: 0 0 5px 0;
            color: #2B5E5F;
            font-size: 1.1em;
        }
        .user-stats {
            color: #6b7280;
            font-size: 0.9em;
        }
        .modules-section {
            margin-top: 15px;
        }
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .module-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }
        .module-toggle:hover {
            background: #f3f4f6;
        }
        .module-toggle.active {
            background: #ecfdf5;
            border-color: #22c55e;
        }
        .module-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .module-icon {
            font-size: 1.1em;
        }
        .module-name {
            font-size: 0.85em;
            font-weight: 500;
            color: #374151;
        }
        .toggle-switch {
            position: relative;
            width: 40px;
            height: 20px;
            background: #d1d5db;
            border-radius: 20px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .toggle-switch.active {
            background: #22c55e;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
        .btn-admin {
            background: #2B5E5F;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9em;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn-admin:hover {
            background: #1e4344;
        }
        .btn-success {
            background: #22c55e;
        }
        .btn-success:hover {
            background: #16a34a;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .bulk-actions {
            background: #f0f9ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2B5E5F;
        }
        .search-box {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.9em;
            margin-bottom: 20px;
        }
        .search-box:focus {
            outline: none;
            border-color: #2B5E5F;
            box-shadow: 0 0 0 3px rgba(43, 94, 95, 0.1);
        }
        .save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #22c55e;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            display: none;
            z-index: 1000;
        }
        .permission-count {
            background: #2B5E5F;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8em;
            font-weight: bold;
        }
    </style>
</head>
<body class="dashboard">
    <div class="save-indicator" id="saveIndicator">
        ✓ Changes saved successfully!
    </div>

    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-logo">
                        <img src="assets/img/logo.png" alt="<?php echo APP_NAME; ?>" class="logo-image">
                    </div>
                    <div class="brand-text">
                        <span class="brand-subtitle">Admin Portal</span>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-menu-container">
                    <button class="user-menu-trigger" onclick="toggleSettings()">
                        <div class="user-avatar-small">
                            <span><?php echo strtoupper(substr($_SESSION['ftp_username'], 0, 2)); ?></span>
                        </div>
                        <div class="user-info-compact">
                            <div class="user-name-compact"><?php echo htmlspecialchars($_SESSION['ftp_username']); ?></div>
                            <div class="user-role-compact">Administrator</div>
                        </div>
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor" class="dropdown-chevron">
                            <path d="M2 4l4 4 4-4H2z"/>
                        </svg>
                    </button>
                    
                    <div class="user-menu-dropdown" id="settingsDropdown">
                        <div class="user-menu-header">
                            <div class="user-profile-info">
                                <div class="user-name-full"><?php echo htmlspecialchars($_SESSION['ftp_username']); ?></div>
                                <div class="user-details-full">
                                    <span class="client-name">System Administrator</span>
                                    <span class="role-badge-small">Admin</span>
                                </div>
                            </div>
                        </div>
                        <div class="menu-divider"></div>
                        <a href="admin_dashboard.php" class="user-menu-item">
                            Admin Dashboard
                        </a>
                        <a href="admin_permissions.php" class="user-menu-item">
                            Module Permissions
                        </a>
                        <a href="dashboard.php" class="user-menu-item">
                            User Dashboard
                        </a>
                        <div class="menu-divider"></div>
                        <a href="logout.php" class="user-menu-item logout-item">
                            Sign Out
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="admin-header">
            <div class="container">
                <div class="breadcrumb">
                    <a href="admin_dashboard.php">Admin Dashboard</a> / User Management
                </div>
                <h1>👥 User Management</h1>
                <p>Manage user permissions and module access across the system</p>
            </div>
        </div>

        <div class="container">
            <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
            <?php endif; ?>

            <!-- Search and Filter -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">🔍 Search & Filter</h2>
                </div>

                <input type="text" id="userSearch" class="search-box"
                       placeholder="Search users by name or filter by module access..."
                       onkeyup="filterUsers()">
            </div>

            <!-- Bulk Actions -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">⚡ Bulk Actions</h2>
                </div>

                <form method="POST" id="bulkForm" class="bulk-actions">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="bulk_update" value="1">

                    <div style="display: flex; gap: 20px; align-items: center; flex-wrap: wrap;">
                        <div>
                            <label style="font-weight: bold; margin-bottom: 5px; display: block;">Select Module:</label>
                            <select id="bulkModule" style="padding: 8px; border: 1px solid #d1d5db; border-radius: 5px;">
                                <option value="">Choose a module...</option>
                                <?php foreach (AVAILABLE_MODULES as $module_key => $module_info): ?>
                                <option value="<?php echo $module_key; ?>">
                                    <?php echo $module_info['icon']; ?> <?php echo $module_info['name']; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div>
                            <label style="font-weight: bold; margin-bottom: 5px; display: block;">Action:</label>
                            <div style="display: flex; gap: 10px;">
                                <button type="button" onclick="bulkToggle(true)" class="btn-admin btn-success">
                                    Grant Access
                                </button>
                                <button type="button" onclick="bulkToggle(false)" class="btn-admin" style="background: #ef4444;">
                                    Revoke Access
                                </button>
                            </div>
                        </div>

                        <div>
                            <label style="font-weight: bold; margin-bottom: 5px; display: block;">Selected Users:</label>
                            <span id="selectedCount" style="color: #6b7280;">0 users selected</span>
                        </div>
                    </div>
                </form>
            </div>

            <!-- User Management Grid -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">👤 System Users</h2>
                    <span style="color: #6b7280;"><?php echo count($all_users) - 1; ?> users (excluding admin)</span>
                </div>

                <div class="users-grid" id="usersGrid">
                    <?php foreach ($all_users as $username => $user_data): ?>
                        <?php if ($username === 'admin') continue; ?>
                        <?php
                        $user_permissions = isset($permissions_matrix[$username]) ? $permissions_matrix[$username] : [];
                        $enabled_count = array_sum($user_permissions);
                        $total_modules = count(AVAILABLE_MODULES);
                        ?>

                        <div class="user-card" data-username="<?php echo $username; ?>">
                            <div class="user-header">
                                <div class="user-avatar">
                                    <?php echo strtoupper(substr($username, 0, 2)); ?>
                                </div>
                                <div class="user-info">
                                    <h3><?php echo htmlspecialchars($username); ?></h3>
                                    <div class="user-stats">
                                        <span class="permission-count"><?php echo $enabled_count; ?> of <?php echo $total_modules; ?></span>
                                        modules enabled
                                    </div>
                                </div>
                                <div>
                                    <input type="checkbox" class="user-selector" value="<?php echo $username; ?>"
                                           onchange="updateSelectedCount()" style="transform: scale(1.2);">
                                </div>
                            </div>

                            <div class="modules-section">
                                <div class="modules-grid">
                                    <?php foreach (AVAILABLE_MODULES as $module_key => $module_info): ?>
                                    <div class="module-toggle <?php echo $user_permissions[$module_key] ? 'active' : ''; ?>"
                                         data-user="<?php echo $username; ?>"
                                         data-module="<?php echo $module_key; ?>"
                                         title="<?php echo htmlspecialchars($module_info['description']); ?>">
                                        <div class="module-info">
                                            <span class="module-icon"><?php echo $module_info['icon']; ?></span>
                                            <span class="module-name"><?php echo $module_info['name']; ?></span>
                                        </div>
                                        <div class="toggle-switch <?php echo $user_permissions[$module_key] ? 'active' : ''; ?>"
                                             onclick="togglePermission('<?php echo $username; ?>', '<?php echo $module_key; ?>', this)">
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div style="margin-top: 30px; text-align: center;">
                    <button type="button" onclick="saveAllChanges()" class="btn-admin btn-success" style="font-size: 1.1em; padding: 15px 30px;">
                        💾 Save All Changes
                    </button>
                    <a href="admin_dashboard.php" class="btn-admin" style="margin-left: 15px;">
                        ← Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </main>

    <script>
        let pendingChanges = {};
        let selectedUsers = new Set();

        function toggleSettings() {
            const dropdown = document.getElementById('settingsDropdown');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        function togglePermission(username, module, toggleElement) {
            const moduleToggle = toggleElement.parentElement;
            const isActive = toggleElement.classList.contains('active');

            // Toggle visual state
            toggleElement.classList.toggle('active');
            moduleToggle.classList.toggle('active');

            // Store pending change
            if (!pendingChanges[username]) {
                pendingChanges[username] = {};
            }
            pendingChanges[username][module] = !isActive;

            // Update permission count
            updatePermissionCount(username);

            // Show save indicator
            showSaveNeeded();
        }

        function updatePermissionCount(username) {
            const userCard = document.querySelector(`[data-username="${username}"]`);
            const activeToggles = userCard.querySelectorAll('.toggle-switch.active').length;
            const totalModules = userCard.querySelectorAll('.toggle-switch').length;
            const countElement = userCard.querySelector('.permission-count');
            countElement.textContent = `${activeToggles} of ${totalModules}`;
        }

        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('.user-selector:checked');
            selectedUsers.clear();
            checkboxes.forEach(cb => selectedUsers.add(cb.value));
            document.getElementById('selectedCount').textContent = `${selectedUsers.size} users selected`;
        }

        function bulkToggle(enable) {
            const module = document.getElementById('bulkModule').value;
            if (!module) {
                alert('Please select a module first.');
                return;
            }

            if (selectedUsers.size === 0) {
                alert('Please select at least one user.');
                return;
            }

            selectedUsers.forEach(username => {
                const toggleElement = document.querySelector(`[data-user="${username}"][data-module="${module}"] .toggle-switch`);
                const moduleToggle = toggleElement.parentElement;

                if (enable && !toggleElement.classList.contains('active')) {
                    toggleElement.classList.add('active');
                    moduleToggle.classList.add('active');
                } else if (!enable && toggleElement.classList.contains('active')) {
                    toggleElement.classList.remove('active');
                    moduleToggle.classList.remove('active');
                }

                // Store pending change
                if (!pendingChanges[username]) {
                    pendingChanges[username] = {};
                }
                pendingChanges[username][module] = enable;

                updatePermissionCount(username);
            });

            showSaveNeeded();
        }

        function filterUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const userCards = document.querySelectorAll('.user-card');

            userCards.forEach(card => {
                const username = card.dataset.username.toLowerCase();
                const moduleNames = Array.from(card.querySelectorAll('.module-name'))
                    .map(el => el.textContent.toLowerCase()).join(' ');

                if (username.includes(searchTerm) || moduleNames.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function showSaveNeeded() {
            // Visual indicator that changes need to be saved
            const saveButton = document.querySelector('button[onclick="saveAllChanges()"]');
            saveButton.style.background = '#f59e0b';
            saveButton.innerHTML = '⚠️ Save Pending Changes';
        }

        function saveAllChanges() {
            if (Object.keys(pendingChanges).length === 0) {
                alert('No changes to save.');
                return;
            }

            // Create form data
            const formData = new FormData();
            formData.append('csrf_token', '<?php echo $csrf_token; ?>');
            formData.append('bulk_update', '1');

            // Add all pending changes
            Object.keys(pendingChanges).forEach(username => {
                Object.keys(pendingChanges[username]).forEach(module => {
                    if (pendingChanges[username][module]) {
                        formData.append(`user_${username}_${module}`, '1');
                    }
                });
            });

            // Submit via fetch
            fetch('admin_user_management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                // Show success indicator
                const indicator = document.getElementById('saveIndicator');
                indicator.style.display = 'block';
                setTimeout(() => {
                    indicator.style.display = 'none';
                }, 3000);

                // Reset pending changes
                pendingChanges = {};

                // Reset save button
                const saveButton = document.querySelector('button[onclick="saveAllChanges()"]');
                saveButton.style.background = '#22c55e';
                saveButton.innerHTML = '✓ All Changes Saved';

                setTimeout(() => {
                    saveButton.style.background = '#2B5E5F';
                    saveButton.innerHTML = '💾 Save All Changes';
                }, 2000);
            })
            .catch(error => {
                alert('Error saving changes. Please try again.');
                console.error('Error:', error);
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('settingsDropdown');
            const trigger = document.querySelector('.user-menu-trigger');

            if (!trigger.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
    </script>
</body>
</html>
