<?php
/**
 * Admin User Details Page
 * Detailed view of individual user information and activity
 */

require_once 'config/config.php';

// Require admin access
requireLogin();
if (!isAdmin()) {
    header('Location: dashboard.php');
    exit();
}

// Get user parameter
$selected_user = isset($_GET['user']) ? sanitizeInput($_GET['user']) : '';
if (empty($selected_user)) {
    header('Location: admin_dashboard.php');
    exit();
}

// Get user data
$all_users = getAllDemoUsers();
if (!isset($all_users[$selected_user])) {
    header('Location: admin_dashboard.php?error=user_not_found');
    exit();
}

$user_data = $all_users[$selected_user];
$user_invoices = array_filter(getAllUsersInvoices(), function($inv) use ($selected_user) {
    return $inv['username'] === $selected_user;
});

// Get user payments
$user_payments = [];
if (isset($_SESSION['demo_payments'])) {
    foreach ($_SESSION['demo_payments'] as $payment) {
        if ($payment['user'] === $selected_user) {
            $user_payments[] = $payment;
        }
    }
}

// Calculate user statistics
$total_invoices = count($user_invoices);
$paid_invoices = array_filter($user_invoices, function($inv) { return $inv['payment_status'] === 'paid'; });
$unpaid_invoices = array_filter($user_invoices, function($inv) { return $inv['payment_status'] === 'unpaid'; });
$total_amount = array_sum(array_column($user_invoices, 'amount'));
$paid_amount = array_sum(array_column($paid_invoices, 'amount'));
$outstanding_amount = array_sum(array_column($unpaid_invoices, 'amount'));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Details: <?php echo htmlspecialchars($selected_user); ?> - Admin Portal</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #2B5E5F 0%, #3b82f6 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .breadcrumb {
            color: rgba(255,255,255,0.8);
            margin-bottom: 10px;
        }
        .breadcrumb a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
        }
        .breadcrumb a:hover {
            color: white;
        }
        .user-header {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .user-avatar-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            font-weight: bold;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #3b82f6;
        }
        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2B5E5F;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #6b7280;
            font-size: 0.9em;
        }
        .admin-section {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f3f4f6;
        }
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2B5E5F;
        }
        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .admin-table th,
        .admin-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .admin-table th {
            background: #f9fafb;
            font-weight: bold;
            color: #374151;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-paid {
            background: #d1fae5;
            color: #065f46;
        }
        .status-unpaid {
            background: #fee2e2;
            color: #991b1b;
        }
        .btn-admin {
            background: #2B5E5F;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9em;
            cursor: pointer;
        }
        .btn-admin:hover {
            background: #1e4344;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .info-item {
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
        }
        .info-label {
            font-weight: bold;
            color: #374151;
            margin-bottom: 5px;
        }
        .info-value {
            color: #6b7280;
        }
    </style>
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-logo">
                        <img src="assets/img/logo.png" alt="<?php echo APP_NAME; ?>" class="logo-image">
                    </div>
                    <div class="brand-text">
                        <span class="brand-subtitle">Admin Portal</span>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-menu-container">
                    <button class="user-menu-trigger" onclick="toggleSettings()">
                        <div class="user-avatar-small">
                            <span><?php echo strtoupper(substr($_SESSION['ftp_username'], 0, 2)); ?></span>
                        </div>
                        <div class="user-info-compact">
                            <div class="user-name-compact"><?php echo htmlspecialchars($_SESSION['ftp_username']); ?></div>
                            <div class="user-role-compact">Administrator</div>
                        </div>
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor" class="dropdown-chevron">
                            <path d="M2 4l4 4 4-4H2z"/>
                        </svg>
                    </button>
                    
                    <div class="user-menu-dropdown" id="settingsDropdown">
                        <div class="user-menu-header">
                            <div class="user-profile-info">
                                <div class="user-name-full"><?php echo htmlspecialchars($_SESSION['ftp_username']); ?></div>
                                <div class="user-details-full">
                                    <span class="client-name">System Administrator</span>
                                    <span class="role-badge-small">Admin</span>
                                </div>
                            </div>
                        </div>
                        <div class="menu-divider"></div>
                        <a href="admin_dashboard.php" class="user-menu-item">
                            Admin Dashboard
                        </a>
                        <a href="dashboard.php" class="user-menu-item">
                            User Dashboard
                        </a>
                        <div class="menu-divider"></div>
                        <a href="logout.php" class="user-menu-item logout-item">
                            Sign Out
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="admin-header">
            <div class="container">
                <div class="breadcrumb">
                    <a href="admin_dashboard.php">Admin Dashboard</a> / User Details
                </div>
                <div class="user-header">
                    <div class="user-avatar-large">
                        <?php echo strtoupper(substr($selected_user, 0, 2)); ?>
                    </div>
                    <div>
                        <h1><?php echo htmlspecialchars($selected_user); ?></h1>
                        <p>FTP User Account Details and Activity</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <!-- User Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value"><?php echo $total_invoices; ?></div>
                    <div class="stat-label">Total Invoices</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value"><?php echo count($paid_invoices); ?></div>
                    <div class="stat-label">Paid Invoices</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$<?php echo number_format($paid_amount, 2); ?></div>
                    <div class="stat-label">Total Paid</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$<?php echo number_format($outstanding_amount, 2); ?></div>
                    <div class="stat-label">Outstanding</div>
                </div>
            </div>

            <!-- User Information -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">👤 User Information</h2>
                </div>

                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Username</div>
                        <div class="info-value"><?php echo htmlspecialchars($selected_user); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Account Status</div>
                        <div class="info-value">
                            <span class="status-badge" style="background: #d1fae5; color: #065f46;">
                                <?php echo ucfirst($user_data['status']); ?>
                            </span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Last Login</div>
                        <div class="info-value"><?php echo date('Y-m-d H:i:s', $user_data['last_login']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">FTP Folder</div>
                        <div class="info-value"><?php echo FTP_BASE_PATH . $selected_user . '/'; ?></div>
                    </div>
                </div>
            </div>

            <!-- User Invoices -->
            <div class="admin-section">
                <div class="section-header">
                    <h2 class="section-title">📄 User Invoices</h2>
                    <span><?php echo count($user_invoices); ?> invoices</span>
                </div>

                <?php if (!empty($user_invoices)): ?>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Invoice</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($user_invoices as $invoice): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($invoice['filename']); ?></strong><br>
                                <small style="color: #6b7280;"><?php echo number_format($invoice['size']); ?> bytes</small>
                            </td>
                            <td>
                                <strong>$<?php echo number_format($invoice['amount'], 2); ?></strong>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $invoice['payment_status']; ?>">
                                    <?php echo ucfirst($invoice['payment_status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo date('Y-m-d H:i', $invoice['modified']); ?>
                            </td>
                            <td>
                                <a href="modules/invoices.php?download=<?php echo urlencode($invoice['filename']); ?>"
                                   class="btn-admin" style="font-size: 0.8em; padding: 5px 10px;">
                                    Download
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <div style="text-align: center; color: #6b7280; padding: 30px;">
                    No invoices found for this user.
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script>
        function toggleSettings() {
            const dropdown = document.getElementById('settingsDropdown');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('settingsDropdown');
            const trigger = document.querySelector('.user-menu-trigger');

            if (!trigger.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
    </script>
</body>
</html>
