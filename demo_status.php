<?php
/**
 * Demo Status Page
 * Shows current demo mode status and available test accounts
 */

require_once 'config/config.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Status - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .demo-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #2B5E5F;
        }
        .demo-status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .demo-accounts {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .account-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        .account-item:last-child {
            border-bottom: none;
        }
        .demo-features {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #2B5E5F;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #1e4344;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><?php echo APP_NAME; ?></h1>
            <h2>Demo Mode Status</h2>
        </div>

        <div class="demo-status">
            <h3>🚀 Demo Mode: <?php echo (defined('FTP_DEMO_MODE') && FTP_DEMO_MODE) ? 'ENABLED' : 'DISABLED'; ?></h3>
            <p>The system is currently running in demo mode. No real FTP server is required.</p>
        </div>

        <div class="demo-accounts">
            <h3>📋 Available Demo Accounts</h3>
            <p>Use any of these accounts to test the system:</p>
            
            <div class="account-item">
                <strong>demo</strong>
                <span>Password: password123</span>
            </div>
            <div class="account-item">
                <strong>client1</strong>
                <span>Password: password123</span>
            </div>
            <div class="account-item">
                <strong>client2</strong>
                <span>Password: password123</span>
            </div>
            <div class="account-item">
                <strong>testuser</strong>
                <span>Password: password123</span>
            </div>
            <div class="account-item">
                <strong>admin</strong>
                <span>Password: admin123</span>
            </div>
        </div>

        <div class="demo-features">
            <h3>✨ Demo Features Available</h3>
            <ul class="feature-list">
                <li>FTP-based authentication simulation</li>
                <li>Dynamic PDF invoice generation</li>
                <li>Payment processing simulation</li>
                <li>Session-based payment tracking</li>
                <li>Secure file download simulation</li>
                <li>Multi-user demo data</li>
                <li>All security features enabled</li>
            </ul>
        </div>

        <?php if (isLoggedIn()): ?>
        <div class="demo-status">
            <h3>👤 Current Session</h3>
            <p><strong>Logged in as:</strong> <?php echo htmlspecialchars($_SESSION['ftp_username']); ?></p>
            <p><strong>User folder:</strong> <?php echo htmlspecialchars($_SESSION['user_folder']); ?></p>
            <p><strong>Session started:</strong> <?php echo date('Y-m-d H:i:s', $_SESSION['last_activity']); ?></p>
            
            <?php if (isset($_SESSION['demo_payments']) && !empty($_SESSION['demo_payments'])): ?>
            <h4>💳 Demo Payments Made:</h4>
            <?php foreach ($_SESSION['demo_payments'] as $payment): ?>
                <div style="background: #fff; padding: 10px; margin: 5px 0; border-radius: 3px;">
                    <strong><?php echo htmlspecialchars($payment['invoice_name']); ?></strong> - 
                    $<?php echo htmlspecialchars($payment['amount']); ?> - 
                    <?php echo htmlspecialchars($payment['payment_date']); ?>
                </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="action-buttons">
            <?php if (isLoggedIn()): ?>
                <a href="dashboard.php" class="btn">Go to Dashboard</a>
                <a href="modules/invoices.php" class="btn">View Invoices</a>
                <a href="logout.php" class="btn btn-secondary">Logout</a>
            <?php else: ?>
                <a href="login.php" class="btn">Login to Demo</a>
                <a href="test_ftp_auth.php" class="btn btn-secondary">Run Tests</a>
            <?php endif; ?>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; text-align: center; color: #6c757d;">
            <p><strong>Note:</strong> This is a development demo. In production, set <code>FTP_DEMO_MODE</code> to <code>false</code> in config.php and configure your real FTP server settings.</p>
        </div>
    </div>
</body>
</html>
