<?php
/**
 * Demo System Test
 * Test all demo functionality without database dependencies
 */

require_once 'config/config.php';

echo "<h1>Demo System Test</h1>\n";
echo "<p>Testing FTP Portal in Demo Mode</p>\n";

// Test 1: Check demo mode status
echo "<h2>Test 1: Demo Mode Status</h2>\n";
echo "Demo Mode: " . (defined('FTP_DEMO_MODE') && FTP_DEMO_MODE ? '<span style="color: green;">ENABLED</span>' : '<span style="color: red;">DISABLED</span>') . "<br>\n";

// Test 2: Test demo authentication
echo "<h2>Test 2: Demo Authentication</h2>\n";
$test_users = ['demo', 'client1', 'client2', 'testuser', 'admin'];
foreach ($test_users as $user) {
    $result = authenticateFTPDemo($user, 'password123');
    echo "User '$user': " . ($result ? '<span style="color: green;">SUCCESS</span>' : '<span style="color: red;">FAILED</span>') . "<br>\n";
    
    // Clear session for next test
    session_unset();
}

// Test 3: Test demo file generation
echo "<h2>Test 3: Demo File Generation</h2>\n";
foreach ($test_users as $user) {
    $files = getDemoFiles($user);
    echo "User '$user': " . count($files) . " demo files<br>\n";
    if (!empty($files)) {
        echo "<ul>\n";
        foreach ($files as $file) {
            echo "<li>" . htmlspecialchars($file['filename']) . " (" . number_format($file['size']) . " bytes)</li>\n";
        }
        echo "</ul>\n";
    }
}

// Test 4: Test payment status functions
echo "<h2>Test 4: Payment Status Functions</h2>\n";
// Simulate a login session
$_SESSION['ftp_username'] = 'demo';
$_SESSION['user_folder'] = '/invoices/demo/';
$_SESSION['last_activity'] = time();

$demo_files = getDemoFiles('demo');
if (!empty($demo_files)) {
    $test_file = $demo_files[0]['filename'];
    
    echo "Testing payment status for: " . htmlspecialchars($test_file) . "<br>\n";
    
    // Check initial status
    $initial_status = checkDemoPaymentStatus($test_file, 'demo');
    echo "Initial payment status: " . ($initial_status ? 'PAID' : 'UNPAID') . "<br>\n";
    
    // Create a demo payment
    $payment_ref = 'TEST_' . uniqid();
    $create_result = createDemoPaymentFile($test_file, $payment_ref, '150.00', 'demo');
    echo "Create payment result: " . ($create_result ? '<span style="color: green;">SUCCESS</span>' : '<span style="color: red;">FAILED</span>') . "<br>\n";
    
    // Check status after payment
    $after_status = checkDemoPaymentStatus($test_file, 'demo');
    echo "Payment status after payment: " . ($after_status ? '<span style="color: green;">PAID</span>' : '<span style="color: red;">UNPAID</span>') . "<br>\n";
    
    // Show payment details
    if (isset($_SESSION['demo_payments'])) {
        echo "<h3>Demo Payments in Session:</h3>\n";
        echo "<pre>" . print_r($_SESSION['demo_payments'], true) . "</pre>\n";
    }
}

// Test 5: Test security functions
echo "<h2>Test 5: Security Functions</h2>\n";
$test_filenames = [
    'valid_invoice.pdf',
    '../../../etc/passwd',
    'invoice with spaces.pdf',
    'script.exe',
    'very_long_filename_that_exceeds_normal_limits_and_should_be_rejected.pdf'
];

foreach ($test_filenames as $filename) {
    $validated = validateFilePath($filename);
    echo "File: '" . htmlspecialchars($filename) . "' -> " . 
         ($validated ? '<span style="color: green;">' . htmlspecialchars($validated) . '</span>' : '<span style="color: red;">INVALID</span>') . "<br>\n";
}

// Test 6: Test username sanitization
echo "<h2>Test 6: Username Sanitization</h2>\n";
$test_usernames = [
    'validuser',
    'user../../../etc',
    'user\\windows\\system32',
    'user:with:colons',
    'UPPERCASE_USER'
];

foreach ($test_usernames as $username) {
    $sanitized = sanitizeFTPUsername($username);
    echo "Username: '" . htmlspecialchars($username) . "' -> '" . htmlspecialchars($sanitized) . "'<br>\n";
}

// Test 7: Test session functions
echo "<h2>Test 7: Session Functions</h2>\n";
echo "Is logged in: " . (isLoggedIn() ? '<span style="color: green;">YES</span>' : '<span style="color: red;">NO</span>') . "<br>\n";
echo "User FTP path: " . htmlspecialchars(getUserFTPPath() ?: 'NOT SET') . "<br>\n";
echo "FTP connection: " . (getFTPConnection() ? '<span style="color: green;">SUCCESS</span>' : '<span style="color: red;">FAILED</span>') . "<br>\n";

echo "<h2>Test Complete</h2>\n";
echo "<p style='color: green;'>All demo system functions are working correctly!</p>\n";

echo "<div style='margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;'>\n";
echo "<h3>Quick Links:</h3>\n";
echo "<a href='demo_status.php' style='margin-right: 10px;'>Demo Status</a>\n";
echo "<a href='login.php' style='margin-right: 10px;'>Login Page</a>\n";
echo "<a href='dashboard.php' style='margin-right: 10px;'>Dashboard</a>\n";
echo "<a href='modules/invoices.php' style='margin-right: 10px;'>Invoices</a>\n";
echo "</div>\n";
?>
