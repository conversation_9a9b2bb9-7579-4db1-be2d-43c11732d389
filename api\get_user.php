<?php
/**
 * Get User API
 * Returns current user information
 */

require_once '../config/config.php';

// Require login
requireLogin();

header('Content-Type: application/json');

// Return current user info
$response = [
    'success' => true,
    'user' => [
        'username' => $_SESSION['ftp_username'],
        'folder' => $_SESSION['user_folder'],
        'last_activity' => $_SESSION['last_activity']
    ]
];

echo json_encode($response);
exit();

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
    exit();
}

$user_id = (int)$_GET['id'];

$database = new Database();
$db = $database->getConnection();
$user = new User($db);

// Get user data
$user_data = $user->getById($user_id);

if (!$user_data) {
    echo json_encode(['success' => false, 'message' => 'User not found']);
    exit();
}

// Get user rights
$user_rights = $user->getUserRights($user_id);

// Prepare response
$response = [
    'success' => true,
    'user' => [
        'id' => $user_data['id'],
        'name' => $user_data['name'],
        'email' => $user_data['email'],
        'role' => $user_data['role'],
        'client_id' => $user_data['client_id'],
        'status' => $user_data['status'],
        'rights' => $user_rights
    ]
];

echo json_encode($response);
?>
