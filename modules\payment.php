<?php
/**
 * FTP-Based Payment Page
 * Handle invoice payment processing without database
 */

require_once '../config/config.php';

// Require login
requireLogin();

$error_message = '';
$success_message = '';

// Get invoice name from URL
if (!isset($_GET['invoice']) || empty($_GET['invoice'])) {
    header('Location: invoices.php');
    exit();
}

$invoice_name = sanitizeInput($_GET['invoice']);

// Verify invoice exists in user's FTP folder
$user_folder = getUserFTPPath();
$invoice_exists = false;

try {
    $connection = getFTPConnection();
    if ($connection) {
        $files = ftp_nlist($connection, $user_folder);
        if ($files !== false) {
            foreach ($files as $file) {
                if (basename($file) === $invoice_name) {
                    $invoice_exists = true;
                    break;
                }
            }
        }
        ftp_close($connection);
    }
} catch (Exception $e) {
    error_log('FTP Error during payment verification: ' . $e->getMessage());
    $invoice_exists = true; // Allow demo mode
}

if (!$invoice_exists) {
    header('Location: invoices.php?error=invoice_not_found');
    exit();
}

// Calculate invoice amount (simplified for demo)
$invoice_amount = calculateInvoiceAmount($invoice_name);

// Check if payment already exists by looking for payment success file
$payment_completed = false;
try {
    $connection = getFTPConnection();
    if ($connection) {
        $files = ftp_nlist($connection, $user_folder);
        if ($files !== false) {
            $payment_file_pattern = pathinfo($invoice_name, PATHINFO_FILENAME) . '_PAID_';
            foreach ($files as $file) {
                if (strpos(basename($file), $payment_file_pattern) !== false) {
                    $payment_completed = true;
                    break;
                }
            }
        }
        ftp_close($connection);
    }
} catch (Exception $e) {
    error_log('FTP Error during payment check: ' . $e->getMessage());
}

/**
 * Calculate invoice amount based on filename (demo implementation)
 */
function calculateInvoiceAmount($invoice_name) {
    // Simple demo calculation based on filename
    $base_amount = 100.00;
    $hash = crc32($invoice_name);
    $amount = $base_amount + (abs($hash) % 500);
    return number_format($amount, 2, '.', '');
}

/**
 * Create payment success file and upload to FTP
 */
function createPaymentSuccessFile($invoice_name, $payment_reference, $amount) {
    try {
        $connection = getFTPConnection();
        if (!$connection) {
            return false;
        }

        // Generate success file content
        $payment_data = [
            'invoice_name' => $invoice_name,
            'payment_reference' => $payment_reference,
            'amount' => $amount,
            'payment_date' => date('Y-m-d H:i:s'),
            'user' => $_SESSION['ftp_username'],
            'status' => 'PAID'
        ];

        $success_content = "PAYMENT CONFIRMATION\n";
        $success_content .= "===================\n\n";
        $success_content .= "Invoice: " . $payment_data['invoice_name'] . "\n";
        $success_content .= "Reference: " . $payment_data['payment_reference'] . "\n";
        $success_content .= "Amount: $" . $payment_data['amount'] . "\n";
        $success_content .= "Date: " . $payment_data['payment_date'] . "\n";
        $success_content .= "User: " . $payment_data['user'] . "\n";
        $success_content .= "Status: " . $payment_data['status'] . "\n";

        // Create temporary local file
        $temp_file = tempnam(sys_get_temp_dir(), 'payment_success_');
        file_put_contents($temp_file, $success_content);

        // Generate success filename
        $name_without_ext = pathinfo($invoice_name, PATHINFO_FILENAME);
        $success_filename = $name_without_ext . '_PAID_' . date('Ymd_His') . '.txt';
        $remote_path = getUserFTPPath() . $success_filename;

        // Upload to FTP
        $result = ftp_put($connection, $remote_path, $temp_file, FTP_ASCII);

        // Clean up
        unlink($temp_file);
        ftp_close($connection);

        return $result;
    } catch (Exception $e) {
        error_log('Error creating payment success file: ' . $e->getMessage());
        return false;
    }
}

/**
 * Generate PayFast payment form (simplified for demo)
 */
function generatePaymentForm($payment_data) {
    // This would normally generate a proper PayFast form
    // For demo purposes, return a simple form
    return [
        'merchant_id' => PAYFAST_MERCHANT_ID,
        'merchant_key' => PAYFAST_MERCHANT_KEY,
        'amount' => $payment_data['amount'],
        'item_name' => 'Invoice Payment: ' . $payment_data['invoice_name'],
        'payment_reference' => $payment_data['payment_reference']
    ];
}
if ($payment_completed) {
    $error_message = 'This invoice has already been paid.';
}

// Handle payment form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$payment_completed) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid request. Please try again.';
    } else {
        // Generate payment reference
        $payment_reference = 'PAY_' . strtoupper(uniqid());

        // For demo purposes, simulate payment success
        if (isset($_POST['simulate_payment']) && $_POST['simulate_payment'] === 'success') {
            // Create payment success file
            $success = createPaymentSuccessFile($invoice_name, $payment_reference, $invoice_amount);

            if ($success) {
                $success_message = 'Payment completed successfully! Payment reference: ' . $payment_reference;
            } else {
                $error_message = 'Payment processed but failed to create confirmation file.';
            }
        } else {
            // Generate PayFast payment form (simplified for demo)
            $payment_data = [
                'user_name' => $_SESSION['ftp_username'],
                'invoice_name' => $invoice_name,
                'amount' => $invoice_amount,
                'payment_reference' => $payment_reference
            ];

            $payfast_form = generatePaymentForm($payment_data);

            // Redirect to PayFast
            $redirect_to_payfast = true;
        }
    }
}

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-logo">
                        <img src="../assets/img/logo.png" alt="<?php echo APP_NAME; ?>" class="logo-image">
                    </div>
                    <div class="brand-text">
                        <span class="brand-subtitle">Payment Gateway</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <a href="invoices.php" class="btn btn-sm btn-secondary">Back to Invoices</a>
                    <a href="../logout.php" class="btn btn-sm btn-danger">Logout</a>
                </div>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="payment-container">
            <div class="payment-header">
                <h2>💳 Invoice Payment</h2>
                <p>Complete your payment securely using PayFast</p>
            </div>
            
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!$error_message && !$existing_payment): ?>
                <div class="payment-details">
                    <div class="invoice-summary">
                        <h3>Invoice Details</h3>
                        <div class="summary-row">
                            <span class="label">Invoice File:</span>
                            <span class="value"><?php echo htmlspecialchars($invoice_name); ?></span>
                        </div>
                        <div class="summary-row">
                            <span class="label">Client:</span>
                            <span class="value"><?php echo htmlspecialchars($client_data['name']); ?></span>
                        </div>
                        <div class="summary-row">
                            <span class="label">Amount:</span>
                            <span class="value amount">R<?php echo number_format($invoice_amount, 2); ?></span>
                        </div>
                    </div>
                    
                    <div class="payment-form">
                        <h3>Payment Information</h3>
                        <div class="payment-info">
                            <div class="info-item">
                                <strong>Payment Method:</strong> PayFast Secure Payment Gateway
                            </div>
                            <div class="info-item">
                                <strong>Accepted Cards:</strong> Visa, Mastercard, American Express
                            </div>
                            <div class="info-item">
                                <strong>Security:</strong> SSL Encrypted & PCI Compliant
                            </div>
                        </div>
                        
                        <?php if (isset($redirect_to_payfast) && $redirect_to_payfast): ?>
                            <!-- Auto-submit form to PayFast -->
                            <form id="payfast-form" action="<?php echo $payfast_form['url']; ?>" method="POST">
                                <?php foreach ($payfast_form['data'] as $key => $value): ?>
                                    <input type="hidden" name="<?php echo htmlspecialchars($key); ?>" value="<?php echo htmlspecialchars($value); ?>">
                                <?php endforeach; ?>
                            </form>
                            
                            <div class="redirect-message">
                                <div class="spinner"></div>
                                <p>Redirecting to PayFast secure payment gateway...</p>
                                <p><small>If you are not redirected automatically, <a href="#" onclick="document.getElementById('payfast-form').submit();">click here</a>.</small></p>
                            </div>
                            
                            <script>
                                // Auto-submit form after 2 seconds
                                setTimeout(function() {
                                    document.getElementById('payfast-form').submit();
                                }, 2000);
                            </script>
                        <?php else: ?>
                            <form method="POST" class="payment-confirm-form">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                
                                <div class="payment-terms">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="agree_terms" required>
                                        I agree to the <a href="#" onclick="showTerms()">Terms and Conditions</a> and authorize this payment
                                    </label>
                                </div>
                                
                                <div class="payment-actions">
                                    <a href="invoices.php" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary btn-large">
                                        💳 Pay R<?php echo number_format($invoice_amount, 2); ?>
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="security-info">
                    <h3>🔒 Security & Privacy</h3>
                    <div class="security-features">
                        <div class="feature">
                            <strong>SSL Encryption:</strong> All data is encrypted during transmission
                        </div>
                        <div class="feature">
                            <strong>PCI Compliance:</strong> PayFast is PCI DSS Level 1 compliant
                        </div>
                        <div class="feature">
                            <strong>No Storage:</strong> We do not store your card details
                        </div>
                        <div class="feature">
                            <strong>Secure Processing:</strong> Payments processed by PayFast
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>
    
    <script src="../assets/js/script.js"></script>
    <script>
        function showTerms() {
            alert('Terms and Conditions:\n\n1. Payment is final and non-refundable\n2. Invoice amount is as displayed\n3. Payment confirmation will be sent via email\n4. For support, contact your account manager');
        }
    </script>
</body>
</html>

<style>
.payment-container {
    max-width: 800px;
    margin: 0 auto;
}

.payment-header {
    text-align: center;
    margin-bottom: 2rem;
}

.payment-header h2 {
    color: #333;
    margin-bottom: 0.5rem;
}

.payment-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.invoice-summary, .payment-form {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.invoice-summary h3, .payment-form h3 {
    margin-bottom: 1rem;
    color: #333;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #eee;
}

.summary-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.summary-row .label {
    font-weight: 600;
    color: #666;
}

.summary-row .value {
    color: #333;
}

.summary-row .amount {
    font-size: 1.25rem;
    font-weight: bold;
    color: #667eea;
}

.payment-info {
    margin-bottom: 1.5rem;
}

.info-item {
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 5px;
    font-size: 0.9rem;
}

.payment-terms {
    margin-bottom: 1.5rem;
}

.payment-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.redirect-message {
    text-align: center;
    padding: 2rem;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.security-info {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.security-info h3 {
    margin-bottom: 1rem;
    color: #333;
}

.security-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.feature {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 5px;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .payment-details {
        grid-template-columns: 1fr;
    }
    
    .payment-actions {
        flex-direction: column;
    }
    
    .security-features {
        grid-template-columns: 1fr;
    }
}
</style>
